#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最終版 Binance 交易分析器
解決所有API問題並提供替代方案
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import time
import logging
import requests
from dotenv import load_dotenv

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('binance_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FinalBinanceAnalyzer:
    def __init__(self):
        """初始化分析器"""
        load_dotenv()
        
        # 從環境變數讀取API金鑰
        self.api_key = os.getenv('API_KEY')
        self.api_secret = os.getenv('API_SECRET')
        
        if not self.api_key or not self.api_secret:
            raise ValueError("請確保 .env 檔案中包含有效的 API_KEY 和 API_SECRET")
        
        self.api_working = False
        self.manual_mode = False
        
        # 嘗試API連接
        self.test_api_connection()
    
    def test_api_connection(self):
        """測試API連接"""
        try:
            # 測試基本連接
            response = requests.get("https://api.binance.com/api/v3/ping", timeout=10)
            if response.status_code == 200:
                logger.info("✅ Binance API 網路連接正常")
            else:
                logger.warning("⚠️ Binance API 網路連接異常")
                return False
            
            # 嘗試使用python-binance庫
            try:
                from binance.client import Client
                
                # 獲取服務器時間並同步
                server_time_response = requests.get('https://api.binance.com/api/v3/time')
                server_time = server_time_response.json()['serverTime']
                local_time = int(time.time() * 1000)
                time_offset = server_time - local_time
                
                logger.info(f"時間偏移: {time_offset}ms")
                
                # 初始化客戶端
                self.client = Client(self.api_key, self.api_secret)
                
                # 測試簡單的API調用
                try:
                    # 嘗試獲取帳戶信息
                    account_info = self.client.get_account()
                    logger.info("✅ API連接成功")
                    self.api_working = True
                    return True
                except Exception as e:
                    logger.warning(f"⚠️ API權限問題: {e}")
                    self.api_working = False
                    return False
                    
            except Exception as e:
                logger.warning(f"⚠️ python-binance庫連接失敗: {e}")
                self.api_working = False
                return False
                
        except Exception as e:
            logger.error(f"❌ 網路連接失敗: {e}")
            self.api_working = False
            return False
    
    def create_manual_template(self):
        """建立手動輸入模板"""
        template_data = {
            "說明": "由於API權限限制，請手動填入交易數據",
            "填寫指南": {
                "1": "登入您的Binance帳戶",
                "2": "前往 [錢包] > [合約錢包] > [交易歷史]",
                "3": "下載完整的交易歷史CSV檔案",
                "4": "將CSV檔案放在此目錄下，命名為 'binance_trades.csv'",
                "5": "重新執行程式進行分析"
            },
            "CSV檔案格式要求": {
                "必要欄位": [
                    "Date(UTC)",
                    "Symbol", 
                    "Side",
                    "Order",
                    "Executed",
                    "Amount",
                    "Price",
                    "Fee",
                    "Realized PNL"
                ]
            },
            "手動統計模板": {
                "總交易筆數": 0,
                "總交易量_USDT": 0,
                "總已實現盈虧_USDT": 0,
                "總手續費_USDT": 0,
                "淨盈虧_USDT": 0,
                "虧損交易筆數": 0,
                "盈利交易筆數": 0,
                "最大單筆虧損_USDT": 0,
                "最大單筆盈利_USDT": 0,
                "主要虧損交易對": []
            }
        }
        
        # 保存模板
        with open('manual_input_template.json', 'w', encoding='utf-8') as f:
            json.dump(template_data, f, ensure_ascii=False, indent=2)
        
        logger.info("✅ 已建立手動輸入模板: manual_input_template.json")
        return template_data
    
    def analyze_csv_file(self, csv_file='binance_trades.csv'):
        """分析CSV檔案"""
        try:
            if not os.path.exists(csv_file):
                logger.warning(f"❌ 找不到CSV檔案: {csv_file}")
                return None
            
            # 讀取CSV檔案
            df = pd.read_csv(csv_file)
            logger.info(f"✅ 成功讀取CSV檔案，包含 {len(df)} 筆記錄")
            
            # 數據清理和轉換
            if 'Realized PNL' in df.columns:
                df['Realized PNL'] = pd.to_numeric(df['Realized PNL'], errors='coerce')
            if 'Fee' in df.columns:
                df['Fee'] = pd.to_numeric(df['Fee'], errors='coerce')
            if 'Amount' in df.columns:
                df['Amount'] = pd.to_numeric(df['Amount'], errors='coerce')
            
            # 進行分析
            analysis_result = self.perform_analysis(df)
            return analysis_result
            
        except Exception as e:
            logger.error(f"❌ 分析CSV檔案時出錯: {e}")
            return None
    
    def perform_analysis(self, df):
        """執行數據分析"""
        try:
            # 基本統計
            total_trades = len(df)
            
            # 盈虧分析
            if 'Realized PNL' in df.columns:
                total_pnl = df['Realized PNL'].sum()
                loss_trades = df[df['Realized PNL'] < 0]
                profit_trades = df[df['Realized PNL'] > 0]
                
                loss_count = len(loss_trades)
                profit_count = len(profit_trades)
                total_loss = loss_trades['Realized PNL'].sum() if loss_count > 0 else 0
                total_profit = profit_trades['Realized PNL'].sum() if profit_count > 0 else 0
                
                max_loss = loss_trades['Realized PNL'].min() if loss_count > 0 else 0
                max_profit = profit_trades['Realized PNL'].max() if profit_count > 0 else 0
            else:
                total_pnl = 0
                loss_count = 0
                profit_count = 0
                total_loss = 0
                total_profit = 0
                max_loss = 0
                max_profit = 0
            
            # 手續費統計
            total_fee = df['Fee'].sum() if 'Fee' in df.columns else 0
            
            # 交易對統計
            if 'Symbol' in df.columns:
                symbol_stats = df.groupby('Symbol').agg({
                    'Realized PNL': ['sum', 'count'] if 'Realized PNL' in df.columns else 'count'
                }).round(4)
                
                # 虧損最多的交易對
                if 'Realized PNL' in df.columns:
                    symbol_pnl = df.groupby('Symbol')['Realized PNL'].sum().sort_values()
                    top_loss_symbols = symbol_pnl.head(10).to_dict()
                else:
                    top_loss_symbols = {}
            else:
                symbol_stats = pd.DataFrame()
                top_loss_symbols = {}
            
            # 時間分析
            if 'Date(UTC)' in df.columns:
                df['Date(UTC)'] = pd.to_datetime(df['Date(UTC)'])
                first_trade = df['Date(UTC)'].min()
                last_trade = df['Date(UTC)'].max()
                trading_period = (last_trade - first_trade).days
            else:
                first_trade = None
                last_trade = None
                trading_period = 0
            
            analysis_result = {
                'summary': {
                    'total_trades': total_trades,
                    'total_realized_pnl': round(total_pnl, 4),
                    'total_fee': round(total_fee, 4),
                    'net_pnl': round(total_pnl - total_fee, 4),
                    'loss_trades_count': loss_count,
                    'profit_trades_count': profit_count,
                    'total_loss_amount': round(total_loss, 4),
                    'total_profit_amount': round(total_profit, 4),
                    'max_single_loss': round(max_loss, 4),
                    'max_single_profit': round(max_profit, 4),
                    'loss_percentage': round((loss_count / total_trades) * 100, 2) if total_trades > 0 else 0,
                    'first_trade': first_trade.isoformat() if first_trade else None,
                    'last_trade': last_trade.isoformat() if last_trade else None,
                    'trading_period_days': trading_period
                },
                'symbol_stats': symbol_stats,
                'top_loss_symbols': top_loss_symbols,
                'raw_data': df
            }
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"❌ 執行分析時出錯: {e}")
            return None
    
    def save_analysis_to_excel(self, analysis_result, filename=None):
        """保存分析結果到Excel"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"binance_loss_analysis_{timestamp}.xlsx"
        
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 摘要頁
                summary_data = []
                for key, value in analysis_result['summary'].items():
                    summary_data.append({'項目': key, '數值': value})
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='損失分析摘要', index=False)
                
                # 交易對統計
                if not analysis_result['symbol_stats'].empty:
                    analysis_result['symbol_stats'].to_excel(writer, sheet_name='交易對統計')
                
                # 虧損排行
                if analysis_result['top_loss_symbols']:
                    loss_df = pd.DataFrame(list(analysis_result['top_loss_symbols'].items()), 
                                         columns=['交易對', '總虧損'])
                    loss_df.to_excel(writer, sheet_name='虧損排行', index=False)
                
                # 原始數據
                analysis_result['raw_data'].to_excel(writer, sheet_name='原始交易數據', index=False)
            
            logger.info(f"✅ 分析結果已保存到: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"❌ 保存Excel檔案時出錯: {e}")
            return None
    
    def print_analysis_report(self, analysis_result):
        """列印分析報告"""
        print("\n" + "="*80)
        print("🏦 BINANCE 合約交易損失分析報告")
        print("="*80)
        
        summary = analysis_result['summary']
        
        print(f"\n📊 交易統計:")
        print(f"   總交易筆數: {summary['total_trades']:,}")
        print(f"   交易期間: {summary['trading_period_days']} 天")
        print(f"   虧損交易數: {summary['loss_trades_count']:,}")
        print(f"   盈利交易數: {summary['profit_trades_count']:,}")
        print(f"   虧損交易比例: {summary['loss_percentage']:.2f}%")
        
        print(f"\n💰 財務分析:")
        print(f"   總已實現盈虧: {summary['total_realized_pnl']:,.4f} USDT")
        print(f"   總手續費: {summary['total_fee']:,.4f} USDT")
        print(f"   淨損失: {abs(summary['net_pnl']):,.4f} USDT" if summary['net_pnl'] < 0 else f"   淨盈利: {summary['net_pnl']:,.4f} USDT")
        print(f"   總虧損金額: {abs(summary['total_loss_amount']):,.4f} USDT")
        print(f"   總盈利金額: {summary['total_profit_amount']:,.4f} USDT")
        
        print(f"\n📈 極值分析:")
        print(f"   最大單筆虧損: {abs(summary['max_single_loss']):,.4f} USDT")
        print(f"   最大單筆盈利: {summary['max_single_profit']:,.4f} USDT")
        
        if analysis_result['top_loss_symbols']:
            print(f"\n🔻 虧損最多的交易對:")
            for symbol, loss in list(analysis_result['top_loss_symbols'].items())[:5]:
                print(f"   {symbol}: {abs(loss):,.4f} USDT")
        
        print(f"\n⚖️  破產程序相關說明:")
        print(f"   📋 此報告顯示完整的交易損失記錄")
        print(f"   💸 總淨損失: {abs(summary['net_pnl']):,.4f} USDT")
        print(f"   📊 所有交易均為實際市場交易，非侵占行為")
        print(f"   📄 詳細記錄可作為法律證據使用")
        
        print("\n" + "="*80)
