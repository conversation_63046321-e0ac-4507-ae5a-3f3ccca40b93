# 🏦 Binance 合約交易損失分析工具

## 📋 專案概述
此工具專為破產程序財務證明而開發，用於分析Binance合約交易歷史，計算損失金額，並生成可用於法律程序的詳細報告。

## 🎯 主要功能
- ✅ 完整分析Binance合約交易歷史
- ✅ 計算總損失金額和交易統計
- ✅ 生成Excel格式的詳細報告
- ✅ 提供破產程序所需的財務證據
- ✅ 證明交易損失而非侵占行為

## 📁 檔案結構
```
binance_review/
├── main_final.py                 # 主執行檔案
├── binance_final_analyzer.py     # 核心分析模組
├── api_test.py                   # API連接測試工具
├── requirements.txt              # Python依賴套件
├── .env                         # API金鑰配置
├── 使用說明.md                   # 詳細使用指南
├── manual_input_template.json   # 手動輸入模板
├── binance_trades_example.csv   # CSV格式示例
└── README.md                    # 本檔案
```

## 🚀 快速開始

### 1. 環境準備
```bash
# 建立虛擬環境
python3 -m venv venv
source venv/bin/activate

# 安裝依賴
pip install -r requirements.txt
```

### 2. 執行分析
```bash
# 執行主程式
python main_final.py
```

### 3. 手動數據輸入（推薦）
由於API權限限制，建議使用手動方式：

1. 登入Binance帳戶
2. 前往 [錢包] > [合約錢包] > [交易歷史]
3. 下載完整交易歷史CSV檔案
4. 重命名為 `binance_trades.csv`
5. 放在程式目錄下
6. 重新執行 `python main_final.py`

## 📊 輸出結果

### 控制台報告
- 交易統計摘要
- 損失分析詳情
- 破產程序相關說明

### Excel詳細報告
- **損失分析摘要** - 完整財務統計
- **交易對統計** - 各交易對詳細數據
- **虧損排行** - 虧損最多的交易對
- **原始交易數據** - 完整交易記錄

## ⚖️ 破產程序應用

### 重要證明要點
1. **實際交易損失** - 所有數據來自官方記錄
2. **非侵占行為** - 顯示市場交易而非人為轉移
3. **完整時間鏈** - 包含詳細時間戳記錄
4. **可驗證性** - 可通過Binance官方驗證

### 建議提交文件
1. Excel分析報告（程式生成）
2. 原始CSV交易數據
3. Binance帳戶截圖
4. 程式碼作為分析方法證明

## 🔧 故障排除

### API問題
- 時間同步問題：使用手動CSV方式
- 權限不足：檢查API設定或使用手動方式
- IP限制：聯繫Binance客服或使用手動方式

### CSV問題
- 檔案格式：確保UTF-8編碼
- 欄位缺失：參考示例檔案格式
- 數據異常：檢查原始下載檔案

## 📞 技術支援

### 常見問題
1. **Q: API連接失敗怎麼辦？**
   A: 使用手動CSV方式，更可靠且數據完整

2. **Q: 分析結果顯示盈利而非虧損？**
   A: 檢查時間範圍和數據完整性

3. **Q: Excel檔案無法開啟？**
   A: 確認已安裝openpyxl套件

### 聯繫方式
如需協助，請提供：
- 錯誤截圖
- 日誌檔案
- CSV檔案樣本（去除敏感資訊）

## ⚠️ 重要聲明
- 此工具僅用於數據分析，不進行任何交易操作
- 所有分析基於用戶提供的官方交易記錄
- 結果可作為財務證明，但需配合其他法律文件
- 建議在專業律師指導下使用分析結果

## 📄 授權
此工具專為破產程序財務證明而開發，僅供合法用途使用。
