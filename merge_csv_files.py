#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合併多個Binance交易CSV檔案
"""

import pandas as pd
import os
import glob

def merge_binance_csv_files():
    """合併多個CSV檔案"""
    print("🔄 正在搜尋CSV檔案...")
    
    # 搜尋所有CSV檔案
    csv_files = glob.glob("*.csv")
    csv_files = [f for f in csv_files if f != "binance_trades.csv"]  # 排除目標檔案
    
    if not csv_files:
        print("❌ 沒有找到CSV檔案")
        print("請將所有下載的CSV檔案放在此目錄下")
        return
    
    print(f"📁 找到 {len(csv_files)} 個CSV檔案:")
    for f in csv_files:
        print(f"   - {f}")
    
    all_data = []
    
    for csv_file in csv_files:
        try:
            print(f"📖 正在讀取 {csv_file}...")
            df = pd.read_csv(csv_file)
            
            if not df.empty:
                all_data.append(df)
                print(f"   ✅ 成功讀取 {len(df)} 筆記錄")
            else:
                print(f"   ⚠️  檔案為空")
                
        except Exception as e:
            print(f"   ❌ 讀取失敗: {e}")
    
    if not all_data:
        print("❌ 沒有有效的數據")
        return
    
    # 合併所有數據
    print("\n🔗 正在合併數據...")
    merged_df = pd.concat(all_data, ignore_index=True)
    
    # 去除重複記錄
    print("🧹 正在去除重複記錄...")
    original_count = len(merged_df)
    merged_df = merged_df.drop_duplicates()
    final_count = len(merged_df)
    
    print(f"   原始記錄: {original_count:,}")
    print(f"   去重後: {final_count:,}")
    print(f"   移除重複: {original_count - final_count:,}")
    
    # 按時間排序
    if 'Date(UTC)' in merged_df.columns:
        print("📅 正在按時間排序...")
        merged_df['Date(UTC)'] = pd.to_datetime(merged_df['Date(UTC)'])
        merged_df = merged_df.sort_values('Date(UTC)')
    
    # 保存合併後的檔案
    output_file = "binance_trades.csv"
    print(f"\n💾 正在保存到 {output_file}...")
    merged_df.to_csv(output_file, index=False)
    
    print(f"✅ 合併完成！")
    print(f"📊 總記錄數: {final_count:,}")
    
    if 'Date(UTC)' in merged_df.columns:
        first_date = merged_df['Date(UTC)'].min()
        last_date = merged_df['Date(UTC)'].max()
        print(f"📅 時間範圍: {first_date} 到 {last_date}")
    
    print(f"\n🎯 現在可以執行: python main_final.py")

if __name__ == "__main__":
    merge_binance_csv_files()
