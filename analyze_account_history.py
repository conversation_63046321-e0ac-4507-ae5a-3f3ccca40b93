#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析Binance帳戶歷史主程式
"""

import sys
from binance_account_analyzer import BinanceAccountAnalyzer
from datetime import datetime

def print_detailed_report(analysis_result, loss_report):
    """列印詳細報告"""
    print("\n" + "="*100)
    print("🏦 BINANCE 帳戶完整歷史分析報告 (2019-2025)")
    print("="*100)
    
    if not analysis_result:
        print("❌ 沒有分析結果")
        return
    
    summary = analysis_result['summary']
    
    print(f"\n📊 交易統計摘要:")
    print(f"   交易期間: {summary['first_trade']} 至 {summary['last_trade']}")
    print(f"   交易天數: {summary['trading_days']:,} 天")
    print(f"   總交易筆數: {summary['total_trades']:,}")
    print(f"   盈利交易數: {summary['profit_trades_count']:,}")
    print(f"   虧損交易數: {summary['loss_trades_count']:,}")
    print(f"   勝率: {summary['win_rate']:.2f}%")
    
    print(f"\n💰 財務分析:")
    print(f"   總已實現盈虧: {summary['total_realized_pnl']:,.4f} USDT")
    print(f"   總手續費支出: {summary['total_fees']:,.4f} USDT")
    print(f"   淨盈虧: {summary['net_pnl']:,.4f} USDT")
    
    if summary['net_pnl'] < 0:
        print(f"   ⚠️  總淨損失: {abs(summary['net_pnl']):,.4f} USDT")
    else:
        print(f"   ✅ 總淨盈利: {summary['net_pnl']:,.4f} USDT")
    
    print(f"\n📈 盈虧詳情:")
    print(f"   總盈利金額: {summary['total_profit']:,.4f} USDT")
    print(f"   總虧損金額: {summary['total_loss']:,.4f} USDT")
    print(f"   平均盈利: {summary['average_profit']:,.4f} USDT")
    print(f"   平均虧損: {summary['average_loss']:,.4f} USDT")
    print(f"   最大單筆盈利: {summary['largest_profit']:,.4f} USDT")
    print(f"   最大單筆虧損: {abs(summary['largest_loss']):,.4f} USDT")
    
    # 虧損報告
    if loss_report:
        print(f"\n🔻 虧損分析詳情:")
        print(f"   總淨損失: {loss_report['total_net_loss']:,.4f} USDT")
        print(f"   交易虧損: {loss_report['total_trading_loss']:,.4f} USDT")
        print(f"   手續費支出: {loss_report['total_fees_paid']:,.4f} USDT")
        print(f"   虧損交易比例: {loss_report['loss_percentage']:.2f}%")
        print(f"   虧損幣種數量: {loss_report['loss_coins_count']}")
        
        # 虧損最多的幣種
        if 'top_loss_coins' in loss_report and not loss_report['top_loss_coins'].empty:
            print(f"\n💸 虧損最多的幣種 (前10名):")
            for coin, data in loss_report['top_loss_coins'].head(10).iterrows():
                print(f"   {coin}: {abs(data['總盈虧']):,.4f} USDT ({data['交易次數']} 筆交易)")
        
        # 虧損最多的月份
        if 'worst_months' in loss_report and not loss_report['worst_months'].empty:
            print(f"\n📅 虧損最多的月份 (前10名):")
            for month, loss in loss_report['worst_months'].head(10).items():
                print(f"   {month}: {abs(loss):,.4f} USDT")
    
    print(f"\n⚖️  破產程序相關證明:")
    print(f"   📋 此報告涵蓋 {summary['trading_days']:,} 天的完整交易歷史")
    print(f"   💸 總損失金額: {loss_report['total_net_loss']:,.4f} USDT" if loss_report else "無虧損")
    print(f"   📊 所有交易均為實際市場交易，非侵占行為")
    print(f"   📄 詳細記錄可作為法律證據使用")
    print(f"   🔍 數據來源: Binance官方帳戶歷史記錄")
    
    print("\n" + "="*100)

def main():
    """主函數"""
    print("🚀 開始分析Binance帳戶歷史...")
    print("📅 分析期間: 2019年12月 - 2025年1月")
    
    try:
        # 初始化分析器
        analyzer = BinanceAccountAnalyzer()
        
        # 載入CSV檔案
        print("\n📁 正在載入CSV檔案...")
        if not analyzer.load_csv_files():
            print("❌ 載入檔案失敗")
            return
        
        # 分析合約盈虧
        print("\n🔍 正在分析合約交易盈虧...")
        analysis_result = analyzer.analyze_futures_pnl()
        
        if not analysis_result:
            print("❌ 沒有找到合約交易數據")
            return
        
        # 生成虧損報告
        print("\n📊 正在生成虧損報告...")
        loss_report = analyzer.generate_loss_report(analysis_result)
        
        # 列印詳細報告
        print_detailed_report(analysis_result, loss_report)
        
        # 保存到Excel
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"binance_complete_analysis_{timestamp}.xlsx"
        
        print(f"\n💾 正在保存詳細報告到 {filename}...")
        saved_file = analyzer.save_to_excel(analysis_result, loss_report, filename)
        
        if saved_file:
            print(f"✅ 報告已成功保存到: {saved_file}")
            
            print(f"\n📋 Excel檔案包含以下工作表:")
            print(f"   📊 交易摘要 - 完整的交易統計")
            print(f"   💸 虧損分析 - 詳細的虧損統計")
            print(f"   🔻 虧損幣種排行 - 各幣種虧損情況")
            print(f"   📅 虧損月份排行 - 各月份虧損情況")
            print(f"   💰 幣種盈虧分析 - 所有幣種詳細分析")
            print(f"   📈 月度盈虧分析 - 每月盈虧趨勢")
            print(f"   📄 原始交易數據樣本 - 部分原始記錄")
            
            print(f"\n🎯 破產程序使用指南:")
            print(f"   📋 此報告包含從2019年到2025年的完整交易歷史")
            print(f"   💸 清楚顯示所有損失來源和金額")
            print(f"   📊 證明所有損失均為實際交易損失")
            print(f"   ⚖️  可作為破產程序的重要財務證據")
        
    except Exception as e:
        print(f"\n❌ 分析過程中出錯: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
