#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下載完整Binance交易歷史工具
從2019年到現在的所有交易記錄
"""

import os
import pandas as pd
import time
from datetime import datetime, timedelta
from binance.client import Client
from binance.exceptions import BinanceAPIException
from dotenv import load_dotenv
import logging

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FullHistoryDownloader:
    def __init__(self):
        """初始化下載器"""
        load_dotenv()
        
        api_key = os.getenv('API_KEY')
        api_secret = os.getenv('API_SECRET')
        
        if not api_key or not api_secret:
            raise ValueError("請確保 .env 檔案中包含有效的 API_KEY 和 API_SECRET")
        
        self.client = Client(api_key, api_secret)
        
    def get_all_futures_income_history(self, start_date="2019-01-01", end_date=None):
        """獲取所有合約收入歷史"""
        if end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")
        
        logger.info(f"開始下載從 {start_date} 到 {end_date} 的收入歷史...")
        
        # 轉換日期為時間戳
        start_timestamp = int(datetime.strptime(start_date, "%Y-%m-%d").timestamp() * 1000)
        end_timestamp = int(datetime.strptime(end_date, "%Y-%m-%d").timestamp() * 1000)
        
        all_income = []
        current_start = start_timestamp
        
        # 分批獲取數據（每次90天）
        batch_days = 90
        batch_ms = batch_days * 24 * 60 * 60 * 1000
        
        while current_start < end_timestamp:
            current_end = min(current_start + batch_ms, end_timestamp)
            
            try:
                logger.info(f"獲取 {datetime.fromtimestamp(current_start/1000)} 到 {datetime.fromtimestamp(current_end/1000)} 的數據...")
                
                # 獲取已實現盈虧
                pnl_data = self.client.futures_income_history(
                    incomeType='REALIZED_PNL',
                    startTime=current_start,
                    endTime=current_end,
                    limit=1000
                )
                
                # 獲取手續費
                commission_data = self.client.futures_income_history(
                    incomeType='COMMISSION',
                    startTime=current_start,
                    endTime=current_end,
                    limit=1000
                )
                
                all_income.extend(pnl_data)
                all_income.extend(commission_data)
                
                logger.info(f"本批次獲取: PNL {len(pnl_data)} 筆, 手續費 {len(commission_data)} 筆")
                
                time.sleep(0.5)  # API限制延遲
                
            except BinanceAPIException as e:
                logger.error(f"API錯誤: {e}")
                if e.code == -1021:  # 時間戳錯誤
                    logger.error("時間同步問題，建議使用手動下載方式")
                    return None
                elif e.code == -2015:  # 權限不足
                    logger.error("API權限不足，建議使用手動下載方式")
                    return None
                else:
                    logger.warning(f"跳過此批次: {e}")
            
            current_start = current_end + 1
        
        logger.info(f"總共獲取 {len(all_income)} 筆收入記錄")
        return all_income
    
    def get_all_futures_trades(self, symbols=None):
        """獲取所有合約交易記錄"""
        if symbols is None:
            # 獲取所有交易對
            try:
                exchange_info = self.client.futures_exchange_info()
                symbols = [s['symbol'] for s in exchange_info['symbols'] if s['status'] == 'TRADING']
                logger.info(f"找到 {len(symbols)} 個交易對")
            except Exception as e:
                logger.error(f"獲取交易對列表失敗: {e}")
                return None
        
        all_trades = []
        
        for symbol in symbols:
            try:
                logger.info(f"獲取 {symbol} 的交易記錄...")
                
                # 獲取該交易對的所有交易
                trades = self.client.futures_account_trades(symbol=symbol, limit=1000)
                
                if trades:
                    for trade in trades:
                        trade['symbol'] = symbol
                    all_trades.extend(trades)
                    logger.info(f"{symbol}: {len(trades)} 筆交易")
                
                time.sleep(0.1)  # API限制延遲
                
            except BinanceAPIException as e:
                if e.code == -2013:  # 無此交易對
                    continue
                logger.warning(f"獲取 {symbol} 交易記錄失敗: {e}")
                continue
        
        logger.info(f"總共獲取 {len(all_trades)} 筆交易記錄")
        return all_trades
    
    def save_to_csv(self, data, filename, data_type="income"):
        """保存數據到CSV"""
        if not data:
            logger.warning("沒有數據可保存")
            return False
        
        try:
            df = pd.DataFrame(data)
            
            if data_type == "income":
                # 處理收入數據
                df['time'] = pd.to_datetime(df['time'], unit='ms')
                df['income'] = pd.to_numeric(df['income'])
                
                # 重新排列欄位
                columns = ['time', 'symbol', 'incomeType', 'income', 'asset', 'info', 'tranId', 'tradeId']
                df = df.reindex(columns=[col for col in columns if col in df.columns])
                
            elif data_type == "trades":
                # 處理交易數據
                df['time'] = pd.to_datetime(df['time'], unit='ms')
                df['price'] = pd.to_numeric(df['price'])
                df['qty'] = pd.to_numeric(df['qty'])
                df['realizedPnl'] = pd.to_numeric(df['realizedPnl'])
                df['commission'] = pd.to_numeric(df['commission'])
            
            # 按時間排序
            df = df.sort_values('time')
            
            # 保存到CSV
            df.to_csv(filename, index=False, encoding='utf-8')
            logger.info(f"數據已保存到 {filename}")
            
            return True
            
        except Exception as e:
            logger.error(f"保存CSV時出錯: {e}")
            return False

def main():
    """主函數"""
    print("🚀 開始下載完整Binance交易歷史...")
    print("📅 時間範圍: 2019-01-01 到 2025-08-29")
    
    try:
        downloader = FullHistoryDownloader()
        
        # 嘗試獲取收入歷史
        print("\n📊 正在獲取收入歷史...")
        income_data = downloader.get_all_futures_income_history("2019-01-01", "2025-08-29")
        
        if income_data:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            income_filename = f"binance_income_history_{timestamp}.csv"
            downloader.save_to_csv(income_data, income_filename, "income")
        
        # 嘗試獲取交易記錄
        print("\n📈 正在獲取交易記錄...")
        trades_data = downloader.get_all_futures_trades()
        
        if trades_data:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            trades_filename = f"binance_trades_full_{timestamp}.csv"
            downloader.save_to_csv(trades_data, trades_filename, "trades")
            
            # 重命名為標準檔名以供分析
            os.rename(trades_filename, "binance_trades.csv")
            print(f"✅ 完整交易記錄已保存為 binance_trades.csv")
        
        if not income_data and not trades_data:
            print("\n❌ API方式無法獲取數據")
            print("📋 請使用手動下載方式:")
            print("1. 登入Binance網頁版")
            print("2. 前往 錢包 → 合約錢包 → 交易歷史")
            print("3. 設定時間範圍: 2019-01-01 到 2025-08-29")
            print("4. 導出CSV檔案")
            print("5. 重命名為 binance_trades.csv")
        
    except Exception as e:
        print(f"❌ 下載失敗: {e}")
        print("\n📋 建議使用手動下載方式獲取完整歷史數據")

if __name__ == "__main__":
    main()
