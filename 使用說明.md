# Binance 合約交易損失分析工具 - 使用說明

## 🎯 工具目的
此工具專為破產程序財務證明而設計，用於：
- 完整分析Binance合約交易歷史
- 計算總損失金額
- 生成法律證據文件
- 證明交易損失而非侵占行為

## ⚠️ 當前狀況
由於API權限限制（時間同步問題和權限不足），需要使用手動方式獲取數據。

## 📋 手動操作步驟

### 步驟1: 登入Binance帳戶
1. 前往 [Binance官網](https://www.binance.com)
2. 登入您的帳戶

### 步驟2: 下載交易歷史
1. 點擊右上角頭像 → **錢包**
2. 選擇 **合約錢包**
3. 點擊 **交易歷史**
4. 設定時間範圍（建議選擇**全部時間**）
5. 點擊 **導出** 按鈕
6. 選擇 **CSV格式**
7. 下載檔案

### 步驟3: 準備CSV檔案
1. 將下載的CSV檔案重命名為 `binance_trades.csv`
2. 將檔案放在程式目錄下（與main_final.py同一資料夾）

### 步驟4: 執行分析
```bash
source venv/bin/activate
python main_final.py
```

## 📊 CSV檔案格式要求

程式需要以下欄位（Binance標準格式）：
- `Date(UTC)` - 交易時間
- `Symbol` - 交易對
- `Side` - 買入/賣出
- `Order` - 訂單類型
- `Executed` - 執行數量
- `Amount` - 交易金額
- `Price` - 交易價格
- `Fee` - 手續費
- `Realized PNL` - 已實現盈虧

## 📈 分析結果

程式將生成：

### 1. 控制台報告
- 交易統計摘要
- 損失分析
- 破產程序相關說明

### 2. Excel詳細報告
包含以下工作表：
- **損失分析摘要** - 完整的財務統計
- **交易對統計** - 各交易對詳細數據
- **虧損排行** - 虧損最多的交易對
- **原始交易數據** - 完整的交易記錄

## ⚖️ 破產程序使用指南

### 重要數據指標
- **總淨損失** - 證明實際損失金額
- **虧損交易比例** - 顯示交易策略失敗
- **交易期間** - 證明損失時間範圍
- **詳細交易記錄** - 提供完整證據鏈

### 法律證明要點
✓ 所有數據來源於官方Binance交易記錄  
✓ 顯示實際市場交易損失，非人為侵占  
✓ 包含完整的時間戳和交易詳情  
✓ 可作為破產程序的重要財務證據  

### 建議提交的文件
1. Excel分析報告（程式生成）
2. 原始CSV交易數據
3. Binance帳戶截圖證明
4. 本程式碼作為分析方法證明

## 🔧 故障排除

### 如果CSV檔案無法讀取
1. 檢查檔案名稱是否為 `binance_trades.csv`
2. 確認檔案格式為UTF-8編碼
3. 檢查必要欄位是否存在

### 如果分析結果異常
1. 確認時間範圍是否正確
2. 檢查是否包含所有交易類型
3. 驗證數據完整性

### 如果需要技術支援
1. 保留錯誤日誌檔案
2. 提供CSV檔案樣本（去除敏感資訊）
3. 描述具體問題和錯誤信息

## 📞 聯繫資訊
如需協助，請提供：
- 錯誤截圖
- 日誌檔案內容
- CSV檔案格式樣本

---

**重要提醒**: 此工具僅用於分析現有交易數據，不會進行任何交易操作。所有分析結果基於您提供的官方交易記錄。
