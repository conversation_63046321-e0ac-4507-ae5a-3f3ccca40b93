#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最終版主執行檔案 - Binance 交易損失分析
提供API和手動輸入兩種方案
"""

import sys
import os
import traceback
from datetime import datetime
from binance_final_analyzer import FinalBinanceAnalyzer

def main():
    """主函數"""
    print("🏦 Binance 合約交易損失分析工具")
    print("=" * 50)
    print("📋 專為破產程序財務證明而設計")
    print("=" * 50)
    
    try:
        # 初始化分析器
        print("\n🔍 正在檢測API連接狀態...")
        analyzer = FinalBinanceAnalyzer()
        
        analysis_result = None
        
        # 檢查是否有CSV檔案
        csv_file = 'binance_trades.csv'
        if os.path.exists(csv_file):
            print(f"\n📁 發現CSV檔案: {csv_file}")
            print("📊 正在分析CSV數據...")
            analysis_result = analyzer.analyze_csv_file(csv_file)
            
        elif analyzer.api_working:
            print("\n✅ API連接正常，正在獲取交易數據...")
            # 這裡可以添加API數據獲取邏輯
            print("⚠️  由於API權限限制，建議使用手動CSV方式")
            
        else:
            print("\n❌ API連接失敗")
            print("📋 正在建立手動輸入指南...")
            
            # 建立手動輸入模板
            template = analyzer.create_manual_template()
            
            print("\n📖 手動操作指南:")
            print("=" * 40)
            print("1. 登入您的Binance帳戶")
            print("2. 前往 [錢包] > [合約錢包] > [交易歷史]")
            print("3. 選擇時間範圍（建議選擇全部）")
            print("4. 點擊 [導出] 下載CSV檔案")
            print("5. 將下載的CSV檔案重命名為 'binance_trades.csv'")
            print("6. 將檔案放在此程式目錄下")
            print("7. 重新執行此程式")
            print("\n📄 已建立詳細指南檔案: manual_input_template.json")
            
            return
        
        # 如果有分析結果，進行處理
        if analysis_result:
            # 列印分析報告
            analyzer.print_analysis_report(analysis_result)
            
            # 保存到Excel
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"binance_loss_analysis_{timestamp}.xlsx"
            
            print(f"\n💾 正在保存詳細報告到 {filename}...")
            saved_file = analyzer.save_analysis_to_excel(analysis_result, filename)
            
            if saved_file:
                print(f"✅ 報告已成功保存到: {saved_file}")
                
                print("\n📋 Excel檔案包含以下工作表:")
                print("   📊 損失分析摘要 - 完整的財務統計")
                print("   📈 交易對統計 - 各交易對詳細數據")
                print("   🔻 虧損排行 - 虧損最多的交易對")
                print("   📄 原始交易數據 - 完整的交易記錄")
                
                print("\n🎯 破產程序使用說明:")
                print("=" * 40)
                summary = analysis_result['summary']
                net_loss = abs(summary['net_pnl']) if summary['net_pnl'] < 0 else 0
                
                if net_loss > 0:
                    print(f"💸 確認總淨損失: {net_loss:,.4f} USDT")
                    print(f"📊 虧損交易比例: {summary['loss_percentage']:.2f}%")
                    print(f"📈 交易期間: {summary['trading_period_days']} 天")
                    print(f"📋 總交易筆數: {summary['total_trades']:,}")
                    
                    print(f"\n⚖️  法律證明要點:")
                    print(f"   ✓ 所有數據來源於官方Binance交易記錄")
                    print(f"   ✓ 顯示實際市場交易損失，非人為侵占")
                    print(f"   ✓ 包含完整的時間戳和交易詳情")
                    print(f"   ✓ 可作為破產程序的重要財務證據")
                    
                    print(f"\n📋 建議提交的文件:")
                    print(f"   1. {saved_file} (完整分析報告)")
                    print(f"   2. binance_trades.csv (原始交易數據)")
                    print(f"   3. Binance帳戶截圖證明")
                    print(f"   4. 本程式碼作為分析方法證明")
                
                else:
                    print("⚠️  注意: 分析顯示總體為盈利狀態")
                    print("請檢查數據是否完整或時間範圍是否正確")
            
        else:
            print("\n❌ 無法獲取或分析交易數據")
            print("請檢查:")
            print("1. API設定是否正確")
            print("2. CSV檔案是否存在且格式正確")
            print("3. 網路連接是否正常")
            
    except KeyboardInterrupt:
        print("\n❌ 程序被用戶中斷")
        sys.exit(1)
        
    except Exception as e:
        print(f"\n❌ 程序執行出錯: {e}")
        print("\n詳細錯誤信息:")
        traceback.print_exc()
        
        print("\n🔧 故障排除:")
        print("1. 檢查 .env 檔案中的API設定")
        print("2. 確認網路連接正常")
        print("3. 嘗試手動下載CSV檔案進行分析")
        print("4. 聯繫技術支援獲取協助")
        
        sys.exit(1)

def show_welcome():
    """顯示歡迎信息"""
    print("\n" + "="*60)
    print("🏦 BINANCE 合約交易損失分析工具")
    print("="*60)
    print("📋 專為破產程序財務證明設計")
    print("🎯 目標: 證明交易損失而非侵占行為")
    print("📊 功能: 完整分析交易歷史並生成法律證據")
    print("="*60)

if __name__ == "__main__":
    show_welcome()
    main()
