#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改進版 Binance 合約交易紀錄分析器
解決時間同步和權限問題
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from binance.client import Client
from binance.exceptions import BinanceAPIException
from dotenv import load_dotenv
import json
import time
import logging
import requests
from binance.helpers import round_step_size

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('binance_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ImprovedBinanceFuturesAnalyzer:
    def __init__(self):
        """初始化分析器"""
        load_dotenv()
        
        # 從環境變數讀取API金鑰
        api_key = os.getenv('API_KEY')
        api_secret = os.getenv('API_SECRET')
        
        if not api_key or not api_secret:
            raise ValueError("請確保 .env 檔案中包含有效的 API_KEY 和 API_SECRET")
        
        # 初始化Binance客戶端，增加時間同步
        try:
            # 獲取服務器時間進行同步
            server_time = requests.get('https://api.binance.com/api/v3/time').json()
            local_time = int(time.time() * 1000)
            time_offset = server_time['serverTime'] - local_time
            
            logger.info(f"時間偏移: {time_offset}ms")
            
            # 初始化客戶端
            self.client = Client(api_key, api_secret)
            
            # 測試連接 - 先嘗試現貨API
            logger.info("測試API連接...")
            try:
                # 嘗試獲取帳戶狀態
                account_status = self.client.get_account_status()
                logger.info("✅ 現貨API連接成功")
                
                # 嘗試合約API
                try:
                    futures_account = self.client.futures_account()
                    logger.info("✅ 合約API連接成功")
                    logger.info(f"帳戶餘額: {futures_account['totalWalletBalance']} USDT")
                    self.has_futures_permission = True
                except BinanceAPIException as e:
                    if e.code == -2015:
                        logger.warning("⚠️ 合約API權限不足，將嘗試使用現貨API獲取歷史數據")
                        self.has_futures_permission = False
                    else:
                        raise e
                        
            except BinanceAPIException as e:
                if e.code == -2015:
                    logger.error("❌ API金鑰權限不足")
                    raise ValueError("API金鑰權限不足，請檢查API設定")
                else:
                    raise e
                    
        except Exception as e:
            logger.error(f"API連接失敗: {e}")
            raise
    
    def get_all_orders_history(self):
        """獲取所有訂單歷史（現貨和合約）"""
        all_orders = []
        
        try:
            if self.has_futures_permission:
                # 獲取合約訂單
                logger.info("正在獲取合約訂單歷史...")
                futures_orders = self.get_futures_orders()
                all_orders.extend(futures_orders)
            
            # 獲取現貨訂單
            logger.info("正在獲取現貨訂單歷史...")
            spot_orders = self.get_spot_orders()
            all_orders.extend(spot_orders)
            
        except Exception as e:
            logger.error(f"獲取訂單歷史時出錯: {e}")
            
        return all_orders
    
    def get_futures_orders(self):
        """獲取合約訂單"""
        all_orders = []
        
        try:
            # 獲取所有合約交易對
            exchange_info = self.client.futures_exchange_info()
            symbols = [s['symbol'] for s in exchange_info['symbols'] if s['status'] == 'TRADING']
            
            logger.info(f"找到 {len(symbols)} 個合約交易對")
            
            for symbol in symbols[:10]:  # 限制前10個交易對進行測試
                try:
                    logger.info(f"正在獲取 {symbol} 的訂單...")
                    orders = self.client.futures_get_all_orders(symbol=symbol, limit=100)
                    
                    if orders:
                        for order in orders:
                            order['market_type'] = 'futures'
                            order['symbol_name'] = symbol
                        all_orders.extend(orders)
                        logger.info(f"{symbol}: 找到 {len(orders)} 筆訂單")
                    
                    time.sleep(0.1)  # API限制延遲
                    
                except BinanceAPIException as e:
                    if e.code == -1121:  # Invalid symbol
                        continue
                    logger.warning(f"獲取 {symbol} 訂單時出錯: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"獲取合約訂單時出錯: {e}")
            
        return all_orders
    
    def get_spot_orders(self):
        """獲取現貨訂單"""
        all_orders = []
        
        try:
            # 獲取帳戶資訊
            account_info = self.client.get_account()
            
            # 獲取有餘額的資產
            balances = [b for b in account_info['balances'] if float(b['free']) > 0 or float(b['locked']) > 0]
            
            logger.info(f"找到 {len(balances)} 個有餘額的資產")
            
            # 獲取所有訂單歷史
            try:
                # 嘗試獲取所有訂單
                orders = self.client.get_all_orders(limit=100)
                
                for order in orders:
                    order['market_type'] = 'spot'
                all_orders.extend(orders)
                
                logger.info(f"獲取到 {len(orders)} 筆現貨訂單")
                
            except Exception as e:
                logger.warning(f"無法獲取所有現貨訂單: {e}")
                
        except Exception as e:
            logger.error(f"獲取現貨訂單時出錯: {e}")
            
        return all_orders
    
    def analyze_orders(self, orders_data):
        """分析訂單數據"""
        if not orders_data:
            logger.warning("沒有訂單數據可供分析")
            return None
        
        # 轉換為DataFrame
        df = pd.DataFrame(orders_data)
        
        # 基本統計
        total_orders = len(df)
        
        # 按市場類型分組
        market_stats = df.groupby('market_type').size()
        
        # 按狀態分組
        status_stats = df.groupby('status').size() if 'status' in df.columns else {}
        
        analysis_result = {
            'summary': {
                'total_orders': total_orders,
                'market_breakdown': market_stats.to_dict(),
                'status_breakdown': status_stats.to_dict() if status_stats else {},
                'analysis_time': datetime.now().isoformat()
            },
            'raw_data': df
        }
        
        return analysis_result
    
    def save_to_excel(self, analysis_result, filename='binance_orders_analysis.xlsx'):
        """將分析結果保存到Excel檔案"""
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 摘要頁
                summary_df = pd.DataFrame([analysis_result['summary']]).T
                summary_df.columns = ['數值']
                summary_df.to_excel(writer, sheet_name='分析摘要', index_label='項目')
                
                # 原始訂單數據
                if not analysis_result['raw_data'].empty:
                    analysis_result['raw_data'].to_excel(writer, sheet_name='訂單數據', index=False)
            
            logger.info(f"分析結果已保存到 {filename}")
            
        except Exception as e:
            logger.error(f"保存Excel檔案時出錯: {e}")
            raise
    
    def print_summary_report(self, analysis_result):
        """列印摘要報告"""
        print("\n" + "="*80)
        print("BINANCE 交易訂單分析報告")
        print("="*80)
        
        if analysis_result:
            summary = analysis_result['summary']
            print(f"\n📊 分析摘要:")
            print(f"   總訂單數: {summary['total_orders']:,}")
            
            if 'market_breakdown' in summary:
                print(f"   市場分布:")
                for market, count in summary['market_breakdown'].items():
                    print(f"     {market}: {count:,} 筆")
            
            if 'status_breakdown' in summary:
                print(f"   訂單狀態:")
                for status, count in summary['status_breakdown'].items():
                    print(f"     {status}: {count:,} 筆")
        
        print("\n" + "="*80)
