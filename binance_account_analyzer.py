#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Binance 帳戶歷史分析器
專門分析Binance帳戶歷史CSV檔案
"""

import pandas as pd
import numpy as np
from datetime import datetime
import glob
import logging

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BinanceAccountAnalyzer:
    def __init__(self):
        """初始化分析器"""
        self.all_data = None
        self.futures_data = None
        self.spot_data = None
        
    def load_csv_files(self, file_pattern="60c235e8-8434-11f0-afa1-0a8dd44a981d-*.csv"):
        """載入CSV檔案"""
        csv_files = glob.glob(file_pattern)
        
        if not csv_files:
            logger.error("沒有找到符合模式的CSV檔案")
            return False
        
        logger.info(f"找到 {len(csv_files)} 個CSV檔案")
        
        all_dataframes = []
        
        for csv_file in csv_files:
            try:
                logger.info(f"正在讀取 {csv_file}...")
                df = pd.read_csv(csv_file)
                
                if not df.empty:
                    all_dataframes.append(df)
                    logger.info(f"成功讀取 {len(df):,} 筆記錄")
                else:
                    logger.warning(f"檔案 {csv_file} 為空")
                    
            except Exception as e:
                logger.error(f"讀取 {csv_file} 時出錯: {e}")
                continue
        
        if not all_dataframes:
            logger.error("沒有成功讀取任何數據")
            return False
        
        # 合併所有數據
        logger.info("正在合併數據...")
        self.all_data = pd.concat(all_dataframes, ignore_index=True)
        
        # 數據清理
        self.clean_data()
        
        # 分離不同類型的數據
        self.separate_data_types()
        
        logger.info(f"總共載入 {len(self.all_data):,} 筆記錄")
        return True
    
    def clean_data(self):
        """清理數據"""
        if self.all_data is None:
            return
        
        # 轉換時間格式
        self.all_data['UTC_Time'] = pd.to_datetime(self.all_data['UTC_Time'])
        
        # 轉換數值格式
        self.all_data['Change'] = pd.to_numeric(self.all_data['Change'], errors='coerce')
        
        # 去除重複記錄
        original_count = len(self.all_data)
        self.all_data = self.all_data.drop_duplicates()
        final_count = len(self.all_data)
        
        if original_count != final_count:
            logger.info(f"移除了 {original_count - final_count:,} 筆重複記錄")
        
        # 按時間排序
        self.all_data = self.all_data.sort_values('UTC_Time')
        
    def separate_data_types(self):
        """分離不同類型的數據"""
        if self.all_data is None:
            return
        
        # 合約數據
        self.futures_data = self.all_data[
            self.all_data['Account'].str.contains('Futures', na=False)
        ].copy()
        
        # 現貨數據
        self.spot_data = self.all_data[
            self.all_data['Account'] == 'Spot'
        ].copy()
        
        logger.info(f"合約記錄: {len(self.futures_data):,} 筆")
        logger.info(f"現貨記錄: {len(self.spot_data):,} 筆")
    
    def analyze_futures_pnl(self):
        """分析合約盈虧"""
        if self.futures_data is None or self.futures_data.empty:
            logger.warning("沒有合約數據可供分析")
            return None
        
        # 篩選已實現盈虧記錄
        pnl_data = self.futures_data[
            self.futures_data['Operation'] == 'Realized Profit and Loss'
        ].copy()
        
        # 篩選手續費記錄
        fee_data = self.futures_data[
            self.futures_data['Operation'] == 'Fee'
        ].copy()
        
        if pnl_data.empty:
            logger.warning("沒有找到已實現盈虧記錄")
            return None
        
        # 基本統計
        total_pnl = pnl_data['Change'].sum()
        total_fees = abs(fee_data['Change'].sum()) if not fee_data.empty else 0
        net_pnl = total_pnl - total_fees
        
        # 盈虧分析
        profit_trades = pnl_data[pnl_data['Change'] > 0]
        loss_trades = pnl_data[pnl_data['Change'] < 0]
        
        total_profit = profit_trades['Change'].sum() if not profit_trades.empty else 0
        total_loss = abs(loss_trades['Change'].sum()) if not loss_trades.empty else 0
        
        # 時間分析
        first_trade = pnl_data['UTC_Time'].min()
        last_trade = pnl_data['UTC_Time'].max()
        trading_days = (last_trade - first_trade).days
        
        # 按幣種分析
        coin_analysis = pnl_data.groupby('Coin').agg({
            'Change': ['sum', 'count', 'mean', 'min', 'max']
        }).round(4)
        coin_analysis.columns = ['總盈虧', '交易次數', '平均盈虧', '最大虧損', '最大盈利']
        coin_analysis = coin_analysis.sort_values('總盈虧')
        
        # 按月份分析
        pnl_data['年月'] = pnl_data['UTC_Time'].dt.to_period('M')
        monthly_analysis = pnl_data.groupby('年月')['Change'].sum().round(4)
        
        analysis_result = {
            'summary': {
                'total_trades': len(pnl_data),
                'total_realized_pnl': round(total_pnl, 4),
                'total_fees': round(total_fees, 4),
                'net_pnl': round(net_pnl, 4),
                'total_profit': round(total_profit, 4),
                'total_loss': round(total_loss, 4),
                'profit_trades_count': len(profit_trades),
                'loss_trades_count': len(loss_trades),
                'win_rate': round((len(profit_trades) / len(pnl_data)) * 100, 2) if len(pnl_data) > 0 else 0,
                'average_profit': round(profit_trades['Change'].mean(), 4) if not profit_trades.empty else 0,
                'average_loss': round(loss_trades['Change'].mean(), 4) if not loss_trades.empty else 0,
                'largest_profit': round(profit_trades['Change'].max(), 4) if not profit_trades.empty else 0,
                'largest_loss': round(loss_trades['Change'].min(), 4) if not loss_trades.empty else 0,
                'first_trade': first_trade,
                'last_trade': last_trade,
                'trading_days': trading_days
            },
            'coin_analysis': coin_analysis,
            'monthly_analysis': monthly_analysis,
            'raw_pnl_data': pnl_data,
            'raw_fee_data': fee_data
        }
        
        return analysis_result
    
    def generate_loss_report(self, analysis_result):
        """生成詳細虧損報告"""
        if not analysis_result:
            return None
        
        summary = analysis_result['summary']
        coin_analysis = analysis_result['coin_analysis']
        
        # 虧損幣種排行
        loss_coins = coin_analysis[coin_analysis['總盈虧'] < 0].copy()
        loss_coins = loss_coins.sort_values('總盈虧')
        
        # 虧損月份分析
        monthly_analysis = analysis_result['monthly_analysis']
        loss_months = monthly_analysis[monthly_analysis < 0].sort_values()
        
        loss_report = {
            'total_net_loss': abs(summary['net_pnl']) if summary['net_pnl'] < 0 else 0,
            'total_trading_loss': summary['total_loss'],
            'total_fees_paid': summary['total_fees'],
            'loss_coins_count': len(loss_coins),
            'top_loss_coins': loss_coins.head(10),
            'worst_months': loss_months.head(10),
            'loss_percentage': 100 - summary['win_rate'],
            'average_loss_per_trade': summary['average_loss'],
            'largest_single_loss': summary['largest_loss']
        }
        
        return loss_report
    
    def save_to_excel(self, analysis_result, loss_report, filename=None):
        """保存分析結果到Excel"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"binance_complete_analysis_{timestamp}.xlsx"
        
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 摘要頁
                summary_data = []
                for key, value in analysis_result['summary'].items():
                    summary_data.append({'項目': key, '數值': value})
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='交易摘要', index=False)
                
                # 虧損報告
                if loss_report:
                    loss_data = []
                    for key, value in loss_report.items():
                        if not isinstance(value, pd.DataFrame):
                            loss_data.append({'項目': key, '數值': value})
                    
                    loss_df = pd.DataFrame(loss_data)
                    loss_df.to_excel(writer, sheet_name='虧損分析', index=False)
                    
                    # 虧損幣種明細
                    if 'top_loss_coins' in loss_report and not loss_report['top_loss_coins'].empty:
                        loss_report['top_loss_coins'].to_excel(writer, sheet_name='虧損幣種排行')
                    
                    # 虧損月份明細
                    if 'worst_months' in loss_report and not loss_report['worst_months'].empty:
                        worst_months_df = pd.DataFrame({
                            '年月': loss_report['worst_months'].index,
                            '虧損金額': loss_report['worst_months'].values
                        })
                        worst_months_df.to_excel(writer, sheet_name='虧損月份排行', index=False)
                
                # 幣種分析
                analysis_result['coin_analysis'].to_excel(writer, sheet_name='幣種盈虧分析')
                
                # 月度分析
                monthly_df = pd.DataFrame({
                    '年月': analysis_result['monthly_analysis'].index,
                    '盈虧': analysis_result['monthly_analysis'].values
                })
                monthly_df.to_excel(writer, sheet_name='月度盈虧分析', index=False)
                
                # 原始數據（限制行數以避免檔案過大）
                raw_data_sample = analysis_result['raw_pnl_data'].head(10000)
                raw_data_sample.to_excel(writer, sheet_name='原始交易數據樣本', index=False)
            
            logger.info(f"分析結果已保存到 {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"保存Excel檔案時出錯: {e}")
            return None
