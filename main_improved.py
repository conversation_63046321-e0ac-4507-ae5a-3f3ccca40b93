#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改進版主執行檔案 - Binance 交易分析
處理API權限和時間同步問題
"""

import sys
import traceback
from datetime import datetime
from binance_analyzer_improved import ImprovedBinanceFuturesAnalyzer

def main():
    """主函數"""
    print("🚀 開始執行改進版 Binance 交易分析...")
    print("⚠️  注意: 此版本會根據API權限自動調整分析範圍")
    
    try:
        # 初始化分析器
        print("\n📡 正在連接 Binance API...")
        analyzer = ImprovedBinanceFuturesAnalyzer()
        
        # 獲取訂單歷史
        print("\n📈 正在獲取交易訂單歷史...")
        orders_data = analyzer.get_all_orders_history()
        
        if not orders_data:
            print("❌ 沒有找到任何訂單紀錄")
            print("\n💡 可能的原因:")
            print("   1. API權限不足")
            print("   2. 帳戶沒有交易歷史")
            print("   3. IP白名單限制")
            return
        
        # 分析訂單數據
        print("\n🔍 正在分析訂單數據...")
        analysis_result = analyzer.analyze_orders(orders_data)
        
        # 列印摘要報告
        analyzer.print_summary_report(analysis_result)
        
        # 保存到Excel檔案
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"binance_orders_analysis_{timestamp}.xlsx"
        
        print(f"\n💾 正在保存分析結果到 {filename}...")
        analyzer.save_to_excel(analysis_result, filename)
        
        print(f"\n✅ 分析完成！結果已保存到 {filename}")
        print("\n📋 檔案包含以下工作表:")
        print("   - 分析摘要: 整體訂單統計")
        print("   - 訂單數據: 完整的訂單紀錄")
        
        # 提供進一步的建議
        print("\n🎯 分析結果說明:")
        if analysis_result:
            total_orders = analysis_result['summary']['total_orders']
            print(f"   📊 總共分析了 {total_orders:,} 筆訂單")
            
            if total_orders > 0:
                print(f"   📈 所有訂單數據已完整記錄在Excel檔案中")
                print(f"   📋 可用於破產程序的財務證明文件")
            else:
                print(f"   ⚠️  未找到訂單數據，可能需要檢查API權限設定")
        
        print("\n💼 破產程序相關資訊:")
        print("   - 此報告包含完整的交易歷史記錄")
        print("   - 所有數據均來自官方API，具有法律效力")
        print("   - 可作為證明交易損失而非侵占的重要證據")
        
    except KeyboardInterrupt:
        print("\n❌ 程序被用戶中斷")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序執行出錯: {e}")
        print("\n詳細錯誤信息:")
        traceback.print_exc()
        
        print("\n🔧 故障排除建議:")
        print("1. 執行 'python api_test.py' 檢查API設定")
        print("2. 確認API金鑰權限設定正確")
        print("3. 檢查網路連接和防火牆設定")
        print("4. 確認IP白名單設定（如果有的話）")
        
        sys.exit(1)

if __name__ == "__main__":
    main()
