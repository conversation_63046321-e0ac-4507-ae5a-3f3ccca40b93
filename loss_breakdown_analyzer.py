#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虧損分組詳細分析器
"""

import pandas as pd
import numpy as np
from datetime import datetime
import glob
from binance_account_analyzer import BinanceAccountAnalyzer

class LossBreakdownAnalyzer:
    def __init__(self):
        """初始化虧損分析器"""
        self.analyzer = BinanceAccountAnalyzer()
        
    def analyze_detailed_losses(self):
        """詳細分析各種虧損"""
        # 載入數據
        if not self.analyzer.load_csv_files():
            return None
        
        # 獲取合約數據
        futures_data = self.analyzer.futures_data
        
        if futures_data is None or futures_data.empty:
            print("❌ 沒有合約數據")
            return None
        
        # 分離不同類型的記錄
        pnl_data = futures_data[futures_data['Operation'] == 'Realized Profit and Loss'].copy()
        fee_data = futures_data[futures_data['Operation'] == 'Fee'].copy()
        funding_data = futures_data[futures_data['Operation'] == 'Funding Fee'].copy()
        
        print(f"📊 數據統計:")
        print(f"   已實現盈虧記錄: {len(pnl_data):,} 筆")
        print(f"   手續費記錄: {len(fee_data):,} 筆")
        print(f"   資金費率記錄: {len(funding_data):,} 筆")
        
        # 1. 按幣種分組虧損
        coin_losses = self.analyze_losses_by_coin(pnl_data, fee_data, funding_data)
        
        # 2. 按時間分組虧損
        time_losses = self.analyze_losses_by_time(pnl_data, fee_data, funding_data)
        
        # 3. 按虧損類型分組
        type_losses = self.analyze_losses_by_type(pnl_data, fee_data, funding_data)
        
        # 4. 按虧損規模分組
        scale_losses = self.analyze_losses_by_scale(pnl_data)
        
        return {
            'coin_losses': coin_losses,
            'time_losses': time_losses,
            'type_losses': type_losses,
            'scale_losses': scale_losses,
            'raw_data': {
                'pnl': pnl_data,
                'fees': fee_data,
                'funding': funding_data
            }
        }
    
    def analyze_losses_by_coin(self, pnl_data, fee_data, funding_data):
        """按幣種分析虧損"""
        print("\n🔍 正在分析各幣種虧損...")
        
        # 交易虧損
        pnl_losses = pnl_data[pnl_data['Change'] < 0].groupby('Coin')['Change'].agg(['sum', 'count']).round(4)
        pnl_losses.columns = ['交易虧損', '虧損次數']
        
        # 手續費
        fee_losses = fee_data.groupby('Coin')['Change'].sum().round(4)
        
        # 資金費率
        funding_losses = funding_data[funding_data['Change'] < 0].groupby('Coin')['Change'].sum().round(4) if not funding_data.empty else pd.Series()
        
        # 合併數據
        coin_analysis = pd.DataFrame(index=pnl_losses.index)
        coin_analysis['交易虧損'] = abs(pnl_losses['交易虧損'])
        coin_analysis['虧損次數'] = pnl_losses['虧損次數']
        coin_analysis['手續費'] = abs(fee_losses.reindex(coin_analysis.index, fill_value=0))
        coin_analysis['資金費率虧損'] = abs(funding_losses.reindex(coin_analysis.index, fill_value=0))
        coin_analysis['總虧損'] = coin_analysis['交易虧損'] + coin_analysis['手續費'] + coin_analysis['資金費率虧損']
        
        # 排序
        coin_analysis = coin_analysis.sort_values('總虧損', ascending=False)
        
        return coin_analysis
    
    def analyze_losses_by_time(self, pnl_data, fee_data, funding_data):
        """按時間分析虧損"""
        print("📅 正在分析各時期虧損...")
        
        # 按年分析
        pnl_data['年份'] = pnl_data['UTC_Time'].dt.year
        fee_data['年份'] = fee_data['UTC_Time'].dt.year
        if not funding_data.empty:
            funding_data['年份'] = funding_data['UTC_Time'].dt.year
        
        yearly_pnl = pnl_data[pnl_data['Change'] < 0].groupby('年份')['Change'].sum()
        yearly_fees = fee_data.groupby('年份')['Change'].sum()
        yearly_funding = funding_data[funding_data['Change'] < 0].groupby('年份')['Change'].sum() if not funding_data.empty else pd.Series()
        
        # 按月分析
        pnl_data['年月'] = pnl_data['UTC_Time'].dt.to_period('M')
        fee_data['年月'] = fee_data['UTC_Time'].dt.to_period('M')
        if not funding_data.empty:
            funding_data['年月'] = funding_data['UTC_Time'].dt.to_period('M')
        
        monthly_pnl = pnl_data[pnl_data['Change'] < 0].groupby('年月')['Change'].sum()
        monthly_fees = fee_data.groupby('年月')['Change'].sum()
        monthly_funding = funding_data[funding_data['Change'] < 0].groupby('年月')['Change'].sum() if not funding_data.empty else pd.Series()
        
        return {
            'yearly': {
                'trading_losses': abs(yearly_pnl),
                'fees': abs(yearly_fees),
                'funding_losses': abs(yearly_funding)
            },
            'monthly': {
                'trading_losses': abs(monthly_pnl),
                'fees': abs(monthly_fees),
                'funding_losses': abs(monthly_funding)
            }
        }
    
    def analyze_losses_by_type(self, pnl_data, fee_data, funding_data):
        """按虧損類型分析"""
        print("📊 正在分析虧損類型...")
        
        # 交易虧損
        trading_losses = abs(pnl_data[pnl_data['Change'] < 0]['Change'].sum())
        
        # 手續費
        total_fees = abs(fee_data['Change'].sum())
        
        # 資金費率虧損
        funding_losses = abs(funding_data[funding_data['Change'] < 0]['Change'].sum()) if not funding_data.empty else 0
        
        # 資金費率收入
        funding_income = funding_data[funding_data['Change'] > 0]['Change'].sum() if not funding_data.empty else 0
        
        # 淨資金費率
        net_funding = funding_losses - funding_income
        
        type_analysis = {
            '交易虧損': trading_losses,
            '手續費支出': total_fees,
            '資金費率虧損': funding_losses,
            '資金費率收入': funding_income,
            '淨資金費率虧損': max(0, net_funding),
            '總虧損': trading_losses + total_fees + max(0, net_funding)
        }
        
        return type_analysis
    
    def analyze_losses_by_scale(self, pnl_data):
        """按虧損規模分析"""
        print("📏 正在分析虧損規模分布...")
        
        loss_data = pnl_data[pnl_data['Change'] < 0]['Change']
        
        # 定義虧損區間
        bins = [-np.inf, -10000, -5000, -1000, -500, -100, -50, -10, -1, 0]
        labels = [
            '超大虧損(>10,000 USDT)',
            '大虧損(5,000-10,000 USDT)',
            '中等虧損(1,000-5,000 USDT)',
            '較大虧損(500-1,000 USDT)',
            '中虧損(100-500 USDT)',
            '小虧損(50-100 USDT)',
            '較小虧損(10-50 USDT)',
            '微小虧損(1-10 USDT)',
            '極小虧損(<1 USDT)'
        ]
        
        # 分組統計
        loss_groups = pd.cut(loss_data, bins=bins, labels=labels, include_lowest=True)
        scale_analysis = pd.DataFrame({
            '虧損次數': loss_groups.value_counts(),
            '虧損金額': loss_data.groupby(loss_groups).sum().abs()
        }).fillna(0)
        
        # 計算比例
        scale_analysis['次數比例(%)'] = (scale_analysis['虧損次數'] / scale_analysis['虧損次數'].sum() * 100).round(2)
        scale_analysis['金額比例(%)'] = (scale_analysis['虧損金額'] / scale_analysis['虧損金額'].sum() * 100).round(2)
        
        return scale_analysis.sort_values('虧損金額', ascending=False)

def print_detailed_loss_breakdown(analysis_result):
    """列印詳細虧損分組報告"""
    print("\n" + "="*120)
    print("💸 詳細虧損分組分析報告")
    print("="*120)
    
    # 1. 按幣種虧損
    print("\n🪙 各幣種虧損明細:")
    print("-" * 80)
    coin_losses = analysis_result['coin_losses']
    for i, (coin, data) in enumerate(coin_losses.head(15).iterrows()):
        print(f"{i+1:2d}. {coin:8s}: 總虧損 {data['總虧損']:>12,.2f} USDT")
        print(f"           交易虧損 {data['交易虧損']:>10,.2f} + 手續費 {data['手續費']:>8,.2f} + 資金費率 {data['資金費率虧損']:>8,.2f}")
        print(f"           虧損次數: {data['虧損次數']:>6,.0f} 次")
        print()
    
    # 2. 按年份虧損
    print("\n📅 各年份虧損明細:")
    print("-" * 60)
    time_losses = analysis_result['time_losses']
    for year in sorted(time_losses['yearly']['trading_losses'].index):
        trading = time_losses['yearly']['trading_losses'].get(year, 0)
        fees = time_losses['yearly']['fees'].get(year, 0)
        funding = time_losses['yearly']['funding_losses'].get(year, 0)
        total = trading + fees + funding
        print(f"{year}年: 總虧損 {total:>12,.2f} USDT")
        print(f"      交易虧損 {trading:>10,.2f} + 手續費 {fees:>8,.2f} + 資金費率 {funding:>8,.2f}")
        print()
    
    # 3. 按虧損類型
    print("\n📊 虧損類型分析:")
    print("-" * 50)
    type_losses = analysis_result['type_losses']
    for loss_type, amount in type_losses.items():
        print(f"{loss_type:12s}: {amount:>12,.2f} USDT")
    
    # 4. 按虧損規模
    print("\n📏 虧損規模分布:")
    print("-" * 80)
    scale_losses = analysis_result['scale_losses']
    for scale, data in scale_losses.iterrows():
        print(f"{scale:25s}: {data['虧損次數']:>6,.0f} 次 ({data['次數比例(%)']:>5.1f}%) | "
              f"{data['虧損金額']:>12,.2f} USDT ({data['金額比例(%)']:>5.1f}%)")
    
    print("\n" + "="*120)

def main():
    """主函數"""
    print("🔍 開始詳細虧損分組分析...")
    
    try:
        analyzer = LossBreakdownAnalyzer()
        analysis_result = analyzer.analyze_detailed_losses()
        
        if analysis_result:
            print_detailed_loss_breakdown(analysis_result)
            
            # 保存詳細分析到Excel
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"loss_breakdown_analysis_{timestamp}.xlsx"
            
            print(f"\n💾 正在保存詳細分析到 {filename}...")
            
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 幣種虧損
                analysis_result['coin_losses'].to_excel(writer, sheet_name='各幣種虧損明細')
                
                # 年度虧損
                yearly_df = pd.DataFrame({
                    '交易虧損': analysis_result['time_losses']['yearly']['trading_losses'],
                    '手續費': analysis_result['time_losses']['yearly']['fees'],
                    '資金費率虧損': analysis_result['time_losses']['yearly']['funding_losses']
                }).fillna(0)
                yearly_df['總虧損'] = yearly_df.sum(axis=1)
                yearly_df.to_excel(writer, sheet_name='各年度虧損明細')
                
                # 虧損類型
                type_df = pd.DataFrame(list(analysis_result['type_losses'].items()), 
                                     columns=['虧損類型', '金額'])
                type_df.to_excel(writer, sheet_name='虧損類型分析', index=False)
                
                # 虧損規模
                analysis_result['scale_losses'].to_excel(writer, sheet_name='虧損規模分布')
            
            print(f"✅ 詳細分析已保存到: {filename}")
        
    except Exception as e:
        print(f"❌ 分析失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
