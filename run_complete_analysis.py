#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
執行完整盈虧分析
"""

from complete_pnl_analyzer import CompletePnLAnalyzer, print_complete_pnl_report
from datetime import datetime
import pandas as pd

def main():
    """主函數"""
    print("💰 開始完整盈虧分析...")
    
    try:
        analyzer = CompletePnLAnalyzer()
        analysis_result = analyzer.analyze_complete_pnl()
        
        if analysis_result:
            print_complete_pnl_report(analysis_result)
            
            # 保存詳細分析到Excel
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"complete_pnl_analysis_{timestamp}.xlsx"
            
            print(f"\n💾 正在保存完整分析到 {filename}...")
            
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 幣種完整盈虧
                analysis_result['coin_pnl'].to_excel(writer, sheet_name='各幣種完整盈虧')
                
                # 年度完整盈虧
                analysis_result['yearly_pnl'].to_excel(writer, sheet_name='各年度完整盈虧')
                
                # 月度完整盈虧
                monthly_pnl, loss_months = analysis_result['monthly_pnl']
                monthly_pnl.to_excel(writer, sheet_name='各月度完整盈虧')
                loss_months.to_excel(writer, sheet_name='虧損月份排行')
                
                # 交易規模分析
                analysis_result['scale_pnl'].to_excel(writer, sheet_name='交易規模盈虧分布')
            
            print(f"✅ 完整分析已保存到: {filename}")
        
    except Exception as e:
        print(f"❌ 分析失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
