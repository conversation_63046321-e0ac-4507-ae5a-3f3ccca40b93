#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整盈虧分析器 - 顯示各分組的盈利、虧損和淨虧損
"""

import pandas as pd
import numpy as np
from datetime import datetime
import glob
from binance_account_analyzer import BinanceAccountAnalyzer

class CompletePnLAnalyzer:
    def __init__(self):
        """初始化完整盈虧分析器"""
        self.analyzer = BinanceAccountAnalyzer()
        
    def analyze_complete_pnl(self):
        """完整分析各分組的盈虧狀況"""
        # 載入數據
        if not self.analyzer.load_csv_files():
            return None
        
        # 獲取合約數據
        futures_data = self.analyzer.futures_data
        
        if futures_data is None or futures_data.empty:
            print("❌ 沒有合約數據")
            return None
        
        # 分離不同類型的記錄
        pnl_data = futures_data[futures_data['Operation'] == 'Realized Profit and Loss'].copy()
        fee_data = futures_data[futures_data['Operation'] == 'Fee'].copy()
        funding_data = futures_data[futures_data['Operation'] == 'Funding Fee'].copy()
        
        print(f"📊 數據統計:")
        print(f"   已實現盈虧記錄: {len(pnl_data):,} 筆")
        print(f"   手續費記錄: {len(fee_data):,} 筆")
        print(f"   資金費率記錄: {len(funding_data):,} 筆")
        
        # 1. 按幣種完整盈虧分析
        coin_pnl = self.analyze_coin_complete_pnl(pnl_data, fee_data, funding_data)
        
        # 2. 按年度完整盈虧分析
        yearly_pnl = self.analyze_yearly_complete_pnl(pnl_data, fee_data, funding_data)
        
        # 3. 按月度完整盈虧分析
        monthly_pnl = self.analyze_monthly_complete_pnl(pnl_data, fee_data, funding_data)
        
        # 4. 按虧損規模完整分析
        scale_pnl = self.analyze_scale_complete_pnl(pnl_data)
        
        return {
            'coin_pnl': coin_pnl,
            'yearly_pnl': yearly_pnl,
            'monthly_pnl': monthly_pnl,
            'scale_pnl': scale_pnl,
            'raw_data': {
                'pnl': pnl_data,
                'fees': fee_data,
                'funding': funding_data
            }
        }
    
    def analyze_coin_complete_pnl(self, pnl_data, fee_data, funding_data):
        """按幣種完整盈虧分析"""
        print("\n🪙 正在分析各幣種完整盈虧...")
        
        # 按幣種分組分析交易盈虧
        coin_stats = pnl_data.groupby('Coin')['Change'].agg([
            ('總盈虧', 'sum'),
            ('盈利金額', lambda x: x[x > 0].sum()),
            ('虧損金額', lambda x: abs(x[x < 0].sum())),
            ('盈利次數', lambda x: (x > 0).sum()),
            ('虧損次數', lambda x: (x < 0).sum()),
            ('總交易次數', 'count'),
            ('最大盈利', 'max'),
            ('最大虧損', 'min')
        ]).round(4)
        
        # 手續費分析
        fee_stats = fee_data.groupby('Coin')['Change'].sum().abs()
        
        # 資金費率分析
        if not funding_data.empty:
            funding_stats = funding_data.groupby('Coin')['Change'].agg([
                ('資金費率收入', lambda x: x[x > 0].sum()),
                ('資金費率支出', lambda x: abs(x[x < 0].sum())),
                ('淨資金費率', 'sum')
            ]).round(4)
        else:
            funding_stats = pd.DataFrame()
        
        # 合併數據
        coin_analysis = coin_stats.copy()
        coin_analysis['手續費'] = fee_stats.reindex(coin_analysis.index, fill_value=0)
        
        if not funding_stats.empty:
            coin_analysis['資金費率收入'] = funding_stats['資金費率收入'].reindex(coin_analysis.index, fill_value=0)
            coin_analysis['資金費率支出'] = funding_stats['資金費率支出'].reindex(coin_analysis.index, fill_value=0)
            coin_analysis['淨資金費率'] = funding_stats['淨資金費率'].reindex(coin_analysis.index, fill_value=0)
        else:
            coin_analysis['資金費率收入'] = 0
            coin_analysis['資金費率支出'] = 0
            coin_analysis['淨資金費率'] = 0
        
        # 計算淨盈虧
        coin_analysis['淨盈虧'] = (coin_analysis['總盈虧'] - 
                                coin_analysis['手續費'] + 
                                coin_analysis['淨資金費率'])
        
        # 計算勝率
        coin_analysis['勝率(%)'] = (coin_analysis['盈利次數'] / 
                                 coin_analysis['總交易次數'] * 100).round(2)
        
        # 排序（按淨盈虧）
        coin_analysis = coin_analysis.sort_values('淨盈虧')
        
        return coin_analysis
    
    def analyze_yearly_complete_pnl(self, pnl_data, fee_data, funding_data):
        """按年度完整盈虧分析"""
        print("📅 正在分析各年度完整盈虧...")
        
        # 添加年份欄位
        pnl_data['年份'] = pnl_data['UTC_Time'].dt.year
        fee_data['年份'] = fee_data['UTC_Time'].dt.year
        if not funding_data.empty:
            funding_data['年份'] = funding_data['UTC_Time'].dt.year
        
        # 按年份分析交易盈虧
        yearly_stats = pnl_data.groupby('年份')['Change'].agg([
            ('總盈虧', 'sum'),
            ('盈利金額', lambda x: x[x > 0].sum()),
            ('虧損金額', lambda x: abs(x[x < 0].sum())),
            ('盈利次數', lambda x: (x > 0).sum()),
            ('虧損次數', lambda x: (x < 0).sum()),
            ('總交易次數', 'count')
        ]).round(4)
        
        # 手續費分析
        yearly_fees = fee_data.groupby('年份')['Change'].sum().abs()
        
        # 資金費率分析
        if not funding_data.empty:
            yearly_funding = funding_data.groupby('年份')['Change'].agg([
                ('資金費率收入', lambda x: x[x > 0].sum()),
                ('資金費率支出', lambda x: abs(x[x < 0].sum())),
                ('淨資金費率', 'sum')
            ]).round(4)
        else:
            yearly_funding = pd.DataFrame()
        
        # 合併數據
        yearly_analysis = yearly_stats.copy()
        yearly_analysis['手續費'] = yearly_fees.reindex(yearly_analysis.index, fill_value=0)
        
        if not yearly_funding.empty:
            yearly_analysis['資金費率收入'] = yearly_funding['資金費率收入'].reindex(yearly_analysis.index, fill_value=0)
            yearly_analysis['資金費率支出'] = yearly_funding['資金費率支出'].reindex(yearly_analysis.index, fill_value=0)
            yearly_analysis['淨資金費率'] = yearly_funding['淨資金費率'].reindex(yearly_analysis.index, fill_value=0)
        else:
            yearly_analysis['資金費率收入'] = 0
            yearly_analysis['資金費率支出'] = 0
            yearly_analysis['淨資金費率'] = 0
        
        # 計算淨盈虧
        yearly_analysis['淨盈虧'] = (yearly_analysis['總盈虧'] - 
                                  yearly_analysis['手續費'] + 
                                  yearly_analysis['淨資金費率'])
        
        # 計算勝率
        yearly_analysis['勝率(%)'] = (yearly_analysis['盈利次數'] / 
                                   yearly_analysis['總交易次數'] * 100).round(2)
        
        return yearly_analysis
    
    def analyze_monthly_complete_pnl(self, pnl_data, fee_data, funding_data):
        """按月度完整盈虧分析"""
        print("📊 正在分析各月度完整盈虧...")
        
        # 添加年月欄位
        pnl_data['年月'] = pnl_data['UTC_Time'].dt.to_period('M')
        fee_data['年月'] = fee_data['UTC_Time'].dt.to_period('M')
        if not funding_data.empty:
            funding_data['年月'] = funding_data['UTC_Time'].dt.to_period('M')
        
        # 按月份分析交易盈虧
        monthly_stats = pnl_data.groupby('年月')['Change'].agg([
            ('總盈虧', 'sum'),
            ('盈利金額', lambda x: x[x > 0].sum()),
            ('虧損金額', lambda x: abs(x[x < 0].sum())),
            ('盈利次數', lambda x: (x > 0).sum()),
            ('虧損次數', lambda x: (x < 0).sum()),
            ('總交易次數', 'count')
        ]).round(4)
        
        # 手續費分析
        monthly_fees = fee_data.groupby('年月')['Change'].sum().abs()
        
        # 資金費率分析
        if not funding_data.empty:
            monthly_funding = funding_data.groupby('年月')['Change'].agg([
                ('資金費率收入', lambda x: x[x > 0].sum()),
                ('資金費率支出', lambda x: abs(x[x < 0].sum())),
                ('淨資金費率', 'sum')
            ]).round(4)
        else:
            monthly_funding = pd.DataFrame()
        
        # 合併數據
        monthly_analysis = monthly_stats.copy()
        monthly_analysis['手續費'] = monthly_fees.reindex(monthly_analysis.index, fill_value=0)
        
        if not monthly_funding.empty:
            monthly_analysis['資金費率收入'] = monthly_funding['資金費率收入'].reindex(monthly_analysis.index, fill_value=0)
            monthly_analysis['資金費率支出'] = monthly_funding['資金費率支出'].reindex(monthly_analysis.index, fill_value=0)
            monthly_analysis['淨資金費率'] = monthly_funding['淨資金費率'].reindex(monthly_analysis.index, fill_value=0)
        else:
            monthly_analysis['資金費率收入'] = 0
            monthly_analysis['資金費率支出'] = 0
            monthly_analysis['淨資金費率'] = 0
        
        # 計算淨盈虧
        monthly_analysis['淨盈虧'] = (monthly_analysis['總盈虧'] - 
                                   monthly_analysis['手續費'] + 
                                   monthly_analysis['淨資金費率'])
        
        # 計算勝率
        monthly_analysis['勝率(%)'] = (monthly_analysis['盈利次數'] / 
                                    monthly_analysis['總交易次數'] * 100).round(2)
        
        # 只返回淨虧損的月份，按虧損金額排序
        loss_months = monthly_analysis[monthly_analysis['淨盈虧'] < 0].sort_values('淨盈虧')
        
        return monthly_analysis, loss_months
    
    def analyze_scale_complete_pnl(self, pnl_data):
        """按交易規模完整盈虧分析"""
        print("📏 正在分析各交易規模完整盈虧...")
        
        # 定義規模區間
        bins = [-np.inf, -10000, -5000, -1000, -500, -100, -50, -10, -1, 0, 1, 10, 50, 100, 500, 1000, 5000, 10000, np.inf]
        labels = [
            '超大虧損(>10,000)',
            '大虧損(5,000-10,000)',
            '中等虧損(1,000-5,000)',
            '較大虧損(500-1,000)',
            '中虧損(100-500)',
            '小虧損(50-100)',
            '較小虧損(10-50)',
            '微小虧損(1-10)',
            '極小虧損(<1)',
            '極小盈利(<1)',
            '微小盈利(1-10)',
            '較小盈利(10-50)',
            '小盈利(50-100)',
            '中盈利(100-500)',
            '較大盈利(500-1,000)',
            '中等盈利(1,000-5,000)',
            '大盈利(5,000-10,000)',
            '超大盈利(>10,000)'
        ]
        
        # 分組統計
        scale_groups = pd.cut(pnl_data['Change'], bins=bins, labels=labels, include_lowest=True)
        scale_analysis = pd.DataFrame({
            '交易次數': scale_groups.value_counts(),
            '總金額': pnl_data.groupby(scale_groups)['Change'].sum()
        }).fillna(0).round(4)
        
        # 計算比例
        scale_analysis['次數比例(%)'] = (scale_analysis['交易次數'] / scale_analysis['交易次數'].sum() * 100).round(2)
        scale_analysis['金額比例(%)'] = (scale_analysis['總金額'] / scale_analysis['總金額'].sum() * 100).round(2)
        
        return scale_analysis

def print_complete_pnl_report(analysis_result):
    """列印完整盈虧報告"""
    print("\n" + "="*150)
    print("💰 完整盈虧分析報告 - 各分組盈利、虧損與淨虧損")
    print("="*150)
    
    # 1. 幣種完整盈虧
    print("\n🪙 各幣種完整盈虧分析:")
    print("-" * 120)
    print(f"{'幣種':8s} {'盈利金額':>12s} {'虧損金額':>12s} {'手續費':>10s} {'資金費率':>10s} {'淨盈虧':>12s} {'勝率':>8s} {'交易次數':>8s}")
    print("-" * 120)
    
    coin_pnl = analysis_result['coin_pnl']
    for coin, data in coin_pnl.head(20).iterrows():
        print(f"{coin:8s} "
              f"{data['盈利金額']:>12,.2f} "
              f"{data['虧損金額']:>12,.2f} "
              f"{data['手續費']:>10,.2f} "
              f"{data['淨資金費率']:>10,.2f} "
              f"{data['淨盈虧']:>12,.2f} "
              f"{data['勝率(%)']:>7.1f}% "
              f"{data['總交易次數']:>8,.0f}")
    
    # 2. 年度完整盈虧
    print(f"\n📅 各年度完整盈虧分析:")
    print("-" * 100)
    print(f"{'年份':6s} {'盈利金額':>12s} {'虧損金額':>12s} {'手續費':>10s} {'資金費率':>10s} {'淨盈虧':>12s} {'勝率':>8s}")
    print("-" * 100)
    
    yearly_pnl = analysis_result['yearly_pnl']
    for year, data in yearly_pnl.iterrows():
        print(f"{year:6d} "
              f"{data['盈利金額']:>12,.2f} "
              f"{data['虧損金額']:>12,.2f} "
              f"{data['手續費']:>10,.2f} "
              f"{data['淨資金費率']:>10,.2f} "
              f"{data['淨盈虧']:>12,.2f} "
              f"{data['勝率(%)']:>7.1f}%")
    
    # 3. 虧損最嚴重的月份
    monthly_pnl, loss_months = analysis_result['monthly_pnl']
    print(f"\n📊 虧損最嚴重的月份 (前20名):")
    print("-" * 100)
    print(f"{'年月':8s} {'盈利金額':>12s} {'虧損金額':>12s} {'手續費':>10s} {'資金費率':>10s} {'淨虧損':>12s} {'勝率':>8s}")
    print("-" * 100)
    
    for month, data in loss_months.head(20).iterrows():
        print(f"{str(month):8s} "
              f"{data['盈利金額']:>12,.2f} "
              f"{data['虧損金額']:>12,.2f} "
              f"{data['手續費']:>10,.2f} "
              f"{data['淨資金費率']:>10,.2f} "
              f"{abs(data['淨盈虧']):>12,.2f} "
              f"{data['勝率(%)']:>7.1f}%")
    
    # 4. 交易規模分析
    print(f"\n📏 交易規模盈虧分布:")
    print("-" * 80)
    print(f"{'規模':25s} {'交易次數':>10s} {'總金額':>15s} {'次數比例':>10s} {'金額比例':>10s}")
    print("-" * 80)
    
    scale_pnl = analysis_result['scale_pnl']
    for scale, data in scale_pnl.iterrows():
        if data['交易次數'] > 0:  # 只顯示有交易的規模
            print(f"{scale:25s} "
                  f"{data['交易次數']:>10,.0f} "
                  f"{data['總金額']:>15,.2f} "
                  f"{data['次數比例(%)']:>9.1f}% "
                  f"{data['金額比例(%)']:>9.1f}%")
    
    # 5. 總結
    print(f"\n📋 總結:")
    total_profit = coin_pnl['盈利金額'].sum()
    total_loss = coin_pnl['虧損金額'].sum()
    total_fees = coin_pnl['手續費'].sum()
    total_funding = coin_pnl['淨資金費率'].sum()
    net_pnl = coin_pnl['淨盈虧'].sum()
    
    print(f"   總盈利金額: {total_profit:>15,.2f} USDT")
    print(f"   總虧損金額: {total_loss:>15,.2f} USDT")
    print(f"   總手續費:   {total_fees:>15,.2f} USDT")
    print(f"   淨資金費率: {total_funding:>15,.2f} USDT")
    print(f"   最終淨虧損: {abs(net_pnl):>15,.2f} USDT" if net_pnl < 0 else f"   最終淨盈利: {net_pnl:>15,.2f} USDT")
    
    print("\n" + "="*150)
