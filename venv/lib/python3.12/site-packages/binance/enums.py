from enum import Enum

SYMBOL_TYPE_SPOT = "SPOT"

ORDER_STATUS_NEW = "NEW"
ORDER_STATUS_PARTIALLY_FILLED = "PARTIALLY_FILLED"
ORDER_STATUS_FILLED = "FILLED"
ORDER_STATUS_CANCELED = "CANCELED"
ORDER_STATUS_PENDING_CANCEL = "PENDING_CANCEL"
ORDER_STATUS_REJECTED = "REJECTED"
ORDER_STATUS_EXPIRED = "EXPIRED"

KLINE_INTERVAL_1SECOND = "1s"
KLINE_INTERVAL_1MINUTE = "1m"
KLINE_INTERVAL_3MINUTE = "3m"
KLINE_INTERVAL_5MINUTE = "5m"
KLINE_INTERVAL_15MINUTE = "15m"
KLINE_INTERVAL_30MINUTE = "30m"
KLINE_INTERVAL_1HOUR = "1h"
KLINE_INTERVAL_2HOUR = "2h"
KLINE_INTERVAL_4HOUR = "4h"
KLINE_INTERVAL_6HOUR = "6h"
KLINE_INTERVAL_8HOUR = "8h"
KLINE_INTERVAL_12HOUR = "12h"
KLINE_INTERVAL_1DAY = "1d"
KLINE_INTERVAL_3DAY = "3d"
KLINE_INTERVAL_1WEEK = "1w"
KLINE_INTERVAL_1MONTH = "1M"

SIDE_BUY = "BUY"
SIDE_SELL = "SELL"

ORDER_TYPE_LIMIT = "LIMIT"
ORDER_TYPE_MARKET = "MARKET"
ORDER_TYPE_STOP_LOSS = "STOP_LOSS"
ORDER_TYPE_STOP_LOSS_LIMIT = "STOP_LOSS_LIMIT"
ORDER_TYPE_TAKE_PROFIT = "TAKE_PROFIT"
ORDER_TYPE_TAKE_PROFIT_LIMIT = "TAKE_PROFIT_LIMIT"
ORDER_TYPE_LIMIT_MAKER = "LIMIT_MAKER"

FUTURE_ORDER_TYPE_LIMIT = "LIMIT"
FUTURE_ORDER_TYPE_MARKET = "MARKET"
FUTURE_ORDER_TYPE_STOP = "STOP"
FUTURE_ORDER_TYPE_STOP_MARKET = "STOP_MARKET"
FUTURE_ORDER_TYPE_TAKE_PROFIT = "TAKE_PROFIT"
FUTURE_ORDER_TYPE_TAKE_PROFIT_MARKET = "TAKE_PROFIT_MARKET"
FUTURE_ORDER_TYPE_LIMIT_MAKER = "LIMIT_MAKER"
FUTURE_ORDER_TYPE_TRAILING_STOP_MARKET = "TRAILING_STOP_MARKET"

TIME_IN_FORCE_GTC = "GTC"  # Good till cancelled
TIME_IN_FORCE_IOC = "IOC"  # Immediate or cancel
TIME_IN_FORCE_FOK = "FOK"  # Fill or kill
TIME_IN_FORCE_GTX = "GTX"  # Post only order
TIME_IN_FORCE_GTD = "GTD"  # Good till date

ORDER_RESP_TYPE_ACK = "ACK"
ORDER_RESP_TYPE_RESULT = "RESULT"
ORDER_RESP_TYPE_FULL = "FULL"

WEBSOCKET_DEPTH_5 = "5"
WEBSOCKET_DEPTH_10 = "10"
WEBSOCKET_DEPTH_20 = "20"

NO_SIDE_EFFECT_TYPE = "NO_SIDE_EFFECT"
MARGIN_BUY_TYPE = "MARGIN_BUY"
AUTO_REPAY_TYPE = "AUTO_REPAY"


class HistoricalKlinesType(Enum):
    SPOT = 1
    FUTURES = 2
    FUTURES_COIN = 3
    FUTURES_MARK_PRICE = 4
    FUTURES_INDEX_PRICE = 5
    FUTURES_COIN_MARK_PRICE = 6
    FUTURES_COIN_INDEX_PRICE = 7


class FuturesType(Enum):
    USD_M = 1
    COIN_M = 2


class ContractType(Enum):
    PERPETUAL = "perpetual"
    CURRENT_QUARTER = "current_quarter"
    NEXT_QUARTER = "next_quarter"
