info = {
    "name": "dsb",
    "date_order": "DMY",
    "january": [
        "jan",
        "januar",
        "januara"
    ],
    "february": [
        "feb",
        "februar",
        "februara"
    ],
    "march": [
        "měr",
        "měrc",
        "měrca"
    ],
    "april": [
        "apr",
        "apryl",
        "apryla"
    ],
    "may": [
        "maj",
        "maja"
    ],
    "june": [
        "jun",
        "junij",
        "junija"
    ],
    "july": [
        "jul",
        "julij",
        "julija"
    ],
    "august": [
        "awg",
        "awgust",
        "awgusta"
    ],
    "september": [
        "sep",
        "september",
        "septembra"
    ],
    "october": [
        "okt",
        "oktober",
        "oktobra"
    ],
    "november": [
        "now",
        "nowember",
        "nowembra"
    ],
    "december": [
        "dec",
        "december",
        "decembra"
    ],
    "monday": [
        "pón",
        "pónje<PERSON><PERSON>"
    ],
    "tuesday": [
        "wał",
        "wałtora"
    ],
    "wednesday": [
        "srj",
        "srjoda"
    ],
    "thursday": [
        "stw",
        "stwórtk"
    ],
    "friday": [
        "pět",
        "pětk"
    ],
    "saturday": [
        "sob",
        "sobota"
    ],
    "sunday": [
        "nje",
        "njeźela"
    ],
    "am": [
        "dopołdnja"
    ],
    "pm": [
        "wótpołdnja"
    ],
    "year": [
        "l",
        "lěto"
    ],
    "month": [
        "mjas",
        "mjasec"
    ],
    "week": [
        "tyź",
        "tyźeń"
    ],
    "day": [
        "ź",
        "źeń"
    ],
    "hour": [
        "g",
        "góź",
        "góźina"
    ],
    "minute": [
        "m",
        "min",
        "minuta"
    ],
    "second": [
        "s",
        "sek",
        "sekunda"
    ],
    "relative-type": {
        "0 day ago": [
            "źinsa"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "ten mjasec"
        ],
        "0 second ago": [
            "now"
        ],
        "0 week ago": [
            "ten tyźeń"
        ],
        "0 year ago": [
            "lětosa"
        ],
        "1 day ago": [
            "cora"
        ],
        "1 month ago": [
            "slědny mjasec"
        ],
        "1 week ago": [
            "slědny tyźeń"
        ],
        "1 year ago": [
            "łoni"
        ],
        "in 1 day": [
            "witśe"
        ],
        "in 1 month": [
            "pśiducy mjasec"
        ],
        "in 1 week": [
            "pśiducy tyźeń"
        ],
        "in 1 year": [
            "znowa"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "pśed (\\d+[.,]?\\d*) d",
            "pśed (\\d+[.,]?\\d*) dnj",
            "pśed (\\d+[.,]?\\d*) dnjami",
            "pśed (\\d+[.,]?\\d*) dnjom"
        ],
        "\\1 hour ago": [
            "pśed (\\d+[.,]?\\d*) g",
            "pśed (\\d+[.,]?\\d*) góź",
            "pśed (\\d+[.,]?\\d*) góźinami",
            "pśed (\\d+[.,]?\\d*) góźinu"
        ],
        "\\1 minute ago": [
            "pśed (\\d+[.,]?\\d*) m",
            "pśed (\\d+[.,]?\\d*) min",
            "pśed (\\d+[.,]?\\d*) minutami",
            "pśed (\\d+[.,]?\\d*) minutu"
        ],
        "\\1 month ago": [
            "pśed (\\d+[.,]?\\d*) mjas",
            "pśed (\\d+[.,]?\\d*) mjasecami",
            "pśed (\\d+[.,]?\\d*) mjasecom"
        ],
        "\\1 second ago": [
            "pśed (\\d+[.,]?\\d*) s",
            "pśed (\\d+[.,]?\\d*) sek",
            "pśed (\\d+[.,]?\\d*) sekundami",
            "pśed (\\d+[.,]?\\d*) sekundu"
        ],
        "\\1 week ago": [
            "pśed (\\d+[.,]?\\d*) tyź",
            "pśed (\\d+[.,]?\\d*) tyźenjami",
            "pśed (\\d+[.,]?\\d*) tyźenjom"
        ],
        "\\1 year ago": [
            "pśed (\\d+[.,]?\\d*) l",
            "pśed (\\d+[.,]?\\d*) lětami",
            "pśed (\\d+[.,]?\\d*) lětom"
        ],
        "in \\1 day": [
            "za (\\d+[.,]?\\d*) dnj",
            "za (\\d+[.,]?\\d*) dnjow",
            "za (\\d+[.,]?\\d*) ź",
            "za (\\d+[.,]?\\d*) źeń"
        ],
        "in \\1 hour": [
            "za (\\d+[.,]?\\d*) g",
            "za (\\d+[.,]?\\d*) góź",
            "za (\\d+[.,]?\\d*) góźin",
            "za (\\d+[.,]?\\d*) góźinu"
        ],
        "in \\1 minute": [
            "za (\\d+[.,]?\\d*) m",
            "za (\\d+[.,]?\\d*) min",
            "za (\\d+[.,]?\\d*) minutow",
            "za (\\d+[.,]?\\d*) minutu"
        ],
        "in \\1 month": [
            "za (\\d+[.,]?\\d*) mjas",
            "za (\\d+[.,]?\\d*) mjasec",
            "za (\\d+[.,]?\\d*) mjasecow"
        ],
        "in \\1 second": [
            "za (\\d+[.,]?\\d*) s",
            "za (\\d+[.,]?\\d*) sek",
            "za (\\d+[.,]?\\d*) sekundow",
            "za (\\d+[.,]?\\d*) sekundu"
        ],
        "in \\1 week": [
            "za (\\d+[.,]?\\d*) tyź",
            "za (\\d+[.,]?\\d*) tyźenjow",
            "za (\\d+[.,]?\\d*) tyźeń"
        ],
        "in \\1 year": [
            "za (\\d+[.,]?\\d*) l",
            "za (\\d+[.,]?\\d*) lět",
            "za (\\d+[.,]?\\d*) lěto"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
