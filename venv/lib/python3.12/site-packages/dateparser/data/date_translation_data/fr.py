info = {
    "name": "fr",
    "date_order": "DMY",
    "january": [
        "janv",
        "janvier",
        "jan"
    ],
    "february": [
        "févr",
        "février",
        "fév"
    ],
    "march": [
        "mars"
    ],
    "april": [
        "avr",
        "avril"
    ],
    "may": [
        "mai"
    ],
    "june": [
        "juin",
        "jun"
    ],
    "july": [
        "juil",
        "juillet",
        "jul"
    ],
    "august": [
        "août",
        "aoû"
    ],
    "september": [
        "sept",
        "septembre",
        "sep"
    ],
    "october": [
        "oct",
        "octobre"
    ],
    "november": [
        "nov",
        "novembre"
    ],
    "december": [
        "déc",
        "décembre"
    ],
    "monday": [
        "lun",
        "lundi",
        "lu"
    ],
    "tuesday": [
        "mar",
        "mardi",
        "ma"
    ],
    "wednesday": [
        "mer",
        "mercredi",
        "me"
    ],
    "thursday": [
        "jeu",
        "jeudi",
        "je"
    ],
    "friday": [
        "ven",
        "vendredi",
        "ve"
    ],
    "saturday": [
        "sam",
        "samedi",
        "sa"
    ],
    "sunday": [
        "dim",
        "dimanche",
        "di"
    ],
    "am": [
        "am"
    ],
    "pm": [
        "pm"
    ],
    "year": [
        "a",
        "an",
        "année",
        "années",
        "ans"
    ],
    "month": [
        "m",
        "mois"
    ],
    "week": [
        "sem",
        "semaine",
        "semaines"
    ],
    "day": [
        "j",
        "jour",
        "jours"
    ],
    "hour": [
        "h",
        "heure",
        "heures"
    ],
    "minute": [
        "min",
        "minute",
        "minutes"
    ],
    "second": [
        "s",
        "seconde",
        "secondes"
    ],
    "relative-type": {
        "0 day ago": [
            "aujourd'hui"
        ],
        "0 hour ago": [
            "cette heure-ci"
        ],
        "0 minute ago": [
            "cette minute-ci"
        ],
        "0 month ago": [
            "ce mois-ci"
        ],
        "0 second ago": [
            "maintenant"
        ],
        "0 week ago": [
            "cette semaine"
        ],
        "0 year ago": [
            "cette année"
        ],
        "1 day ago": [
            "hier"
        ],
        "1 month ago": [
            "le mois dernier"
        ],
        "1 week ago": [
            "la semaine dernière"
        ],
        "1 year ago": [
            "l'année dernière"
        ],
        "in 1 day": [
            "demain"
        ],
        "in 1 month": [
            "le mois prochain"
        ],
        "in 1 week": [
            "la semaine prochaine"
        ],
        "in 1 year": [
            "l'année prochaine"
        ],
        "2 day ago": [
            "avant-hier"
        ],
        "in 2 day": [
            "après-demain"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "il y a (\\d+[.,]?\\d*) j",
            "il y a (\\d+[.,]?\\d*) jour",
            "il y a (\\d+[.,]?\\d*) jours"
        ],
        "\\1 hour ago": [
            "il y a (\\d+[.,]?\\d*) h",
            "il y a (\\d+[.,]?\\d*) heure",
            "il y a (\\d+[.,]?\\d*) heures",
            "il y a (\\d+[.,]?\\d*)h"
        ],
        "\\1 minute ago": [
            "il y a (\\d+[.,]?\\d*) min",
            "il y a (\\d+[.,]?\\d*) minute",
            "il y a (\\d+[.,]?\\d*) minutes",
            "il y a (\\d+[.,]?\\d*)min"
        ],
        "\\1 month ago": [
            "il y a (\\d+[.,]?\\d*) m",
            "il y a (\\d+[.,]?\\d*) mois"
        ],
        "\\1 second ago": [
            "il y a (\\d+[.,]?\\d*) s",
            "il y a (\\d+[.,]?\\d*) seconde",
            "il y a (\\d+[.,]?\\d*) secondes"
        ],
        "\\1 week ago": [
            "il y a (\\d+[.,]?\\d*) sem",
            "il y a (\\d+[.,]?\\d*) semaine",
            "il y a (\\d+[.,]?\\d*) semaines"
        ],
        "\\1 year ago": [
            "il y a (\\d+[.,]?\\d*) a",
            "il y a (\\d+[.,]?\\d*) an",
            "il y a (\\d+[.,]?\\d*) ans"
        ],
        "in \\1 day": [
            "dans (\\d+[.,]?\\d*) j",
            "dans (\\d+[.,]?\\d*) jour",
            "dans (\\d+[.,]?\\d*) jours"
        ],
        "in \\1 hour": [
            "dans (\\d+[.,]?\\d*) h",
            "dans (\\d+[.,]?\\d*) heure",
            "dans (\\d+[.,]?\\d*) heures",
            "dans (\\d+[.,]?\\d*)h"
        ],
        "in \\1 minute": [
            "dans (\\d+[.,]?\\d*) min",
            "dans (\\d+[.,]?\\d*) minute",
            "dans (\\d+[.,]?\\d*) minutes"
        ],
        "in \\1 month": [
            "dans (\\d+[.,]?\\d*) m",
            "dans (\\d+[.,]?\\d*) mois"
        ],
        "in \\1 second": [
            "dans (\\d+[.,]?\\d*) s",
            "dans (\\d+[.,]?\\d*) seconde",
            "dans (\\d+[.,]?\\d*) secondes"
        ],
        "in \\1 week": [
            "dans (\\d+[.,]?\\d*) sem",
            "dans (\\d+[.,]?\\d*) semaine",
            "dans (\\d+[.,]?\\d*) semaines"
        ],
        "in \\1 year": [
            "dans (\\d+[.,]?\\d*) a",
            "dans (\\d+[.,]?\\d*) an",
            "dans (\\d+[.,]?\\d*) ans"
        ]
    },
    "locale_specific": {
        "fr-BE": {
            "name": "fr-BE"
        },
        "fr-BF": {
            "name": "fr-BF"
        },
        "fr-BI": {
            "name": "fr-BI"
        },
        "fr-BJ": {
            "name": "fr-BJ"
        },
        "fr-BL": {
            "name": "fr-BL"
        },
        "fr-CA": {
            "name": "fr-CA",
            "date_order": "YMD",
            "july": [
                "juill"
            ]
        },
        "fr-CD": {
            "name": "fr-CD"
        },
        "fr-CF": {
            "name": "fr-CF"
        },
        "fr-CG": {
            "name": "fr-CG"
        },
        "fr-CH": {
            "name": "fr-CH"
        },
        "fr-CI": {
            "name": "fr-CI"
        },
        "fr-CM": {
            "name": "fr-CM",
            "am": [
                "mat",
                "matin"
            ],
            "pm": [
                "soir"
            ]
        },
        "fr-DJ": {
            "name": "fr-DJ"
        },
        "fr-DZ": {
            "name": "fr-DZ"
        },
        "fr-GA": {
            "name": "fr-GA"
        },
        "fr-GF": {
            "name": "fr-GF"
        },
        "fr-GN": {
            "name": "fr-GN"
        },
        "fr-GP": {
            "name": "fr-GP"
        },
        "fr-GQ": {
            "name": "fr-GQ"
        },
        "fr-HT": {
            "name": "fr-HT",
            "day": [
                "jr"
            ],
            "hour": [
                "hr"
            ],
            "second": [
                "sec"
            ]
        },
        "fr-KM": {
            "name": "fr-KM"
        },
        "fr-LU": {
            "name": "fr-LU"
        },
        "fr-MA": {
            "name": "fr-MA",
            "january": [
                "jan"
            ],
            "february": [
                "fév"
            ],
            "march": [
                "mar"
            ],
            "june": [
                "jui"
            ]
        },
        "fr-MC": {
            "name": "fr-MC"
        },
        "fr-MF": {
            "name": "fr-MF"
        },
        "fr-MG": {
            "name": "fr-MG"
        },
        "fr-ML": {
            "name": "fr-ML"
        },
        "fr-MQ": {
            "name": "fr-MQ"
        },
        "fr-MR": {
            "name": "fr-MR"
        },
        "fr-MU": {
            "name": "fr-MU"
        },
        "fr-NC": {
            "name": "fr-NC"
        },
        "fr-NE": {
            "name": "fr-NE"
        },
        "fr-PF": {
            "name": "fr-PF"
        },
        "fr-PM": {
            "name": "fr-PM"
        },
        "fr-RE": {
            "name": "fr-RE"
        },
        "fr-RW": {
            "name": "fr-RW"
        },
        "fr-SC": {
            "name": "fr-SC"
        },
        "fr-SN": {
            "name": "fr-SN"
        },
        "fr-SY": {
            "name": "fr-SY"
        },
        "fr-TD": {
            "name": "fr-TD"
        },
        "fr-TG": {
            "name": "fr-TG"
        },
        "fr-TN": {
            "name": "fr-TN"
        },
        "fr-VU": {
            "name": "fr-VU"
        },
        "fr-WF": {
            "name": "fr-WF"
        },
        "fr-YT": {
            "name": "fr-YT"
        }
    },
    "skip": [
        "environ",
        "er",
        "et",
        "le",
        "à",
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ],
    "sentence_splitter_group": 1,
    "ago": [
        "il ya",
        "il y a"
    ],
    "in": [
        "dans",
        "en",
        "après"
    ],
    "simplifications": [
        {
            "d'une": "1"
        },
        {
            "d'un": "1"
        },
        {
            "une": "1"
        },
        {
            "un": "1"
        },
        {
            "(\\d+[.,]?\\d*)\\s+h\\s+(\\d+[.,]?\\d*)\\s+min": "\\1h\\2m"
        },
        {
            "(\\d+[.,]?\\d*)h(\\d+[.,]?\\d*)m?": "\\1:\\2"
        },
        {
            "moins\\s(?:de\\s)?(\\d+[.,]?\\d*)\\s?(minute|seconde|heure)": "\\1 \\2"
        },
        {
            "moins\\s(?:de\\s)?(\\d+[.,]?\\d*)\\s?s": "\\1 seconde"
        },
        {
            "moins\\s(?:de\\s)?(\\d+[.,]?\\d*)\\s?m": "\\1 minute"
        },
        {
            "moins\\s(?:de\\s)?(\\d+[.,]?\\d*)\\s?h": "\\1 heure"
        },
        {
            "deux": "2"
        },
        {
            "trois": "3"
        },
        {
            "quatre": "4"
        },
        {
            "cinq": "5"
        },
        {
            "six": "6"
        },
        {
            "sept": "7"
        },
        {
            "huit": "8"
        },
        {
            "neuf": "9"
        },
        {
            "dix": "10"
        },
        {
            "onze": "11"
        },
        {
            "douze": "12"
        }
    ]
}
