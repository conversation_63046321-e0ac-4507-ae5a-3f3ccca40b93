info = {
    "name": "lrc",
    "date_order": "YMD",
    "january": [
        "جانڤیە"
    ],
    "february": [
        "فئڤریە"
    ],
    "march": [
        "مارس"
    ],
    "april": [
        "آڤریل"
    ],
    "may": [
        "مئی"
    ],
    "june": [
        "جوٙأن"
    ],
    "july": [
        "جوٙلا"
    ],
    "august": [
        "آگوست"
    ],
    "september": [
        "سئپتامر"
    ],
    "october": [
        "ئوکتوڤر"
    ],
    "november": [
        "نوڤامر"
    ],
    "december": [
        "دئسامر"
    ],
    "monday": [
        "mon"
    ],
    "tuesday": [
        "tue"
    ],
    "wednesday": [
        "wed"
    ],
    "thursday": [
        "thu"
    ],
    "friday": [
        "fri"
    ],
    "saturday": [
        "sat"
    ],
    "sunday": [
        "sun"
    ],
    "am": [
        "am"
    ],
    "pm": [
        "pm"
    ],
    "year": [
        "سال"
    ],
    "month": [
        "ما"
    ],
    "week": [
        "ھأفتە"
    ],
    "day": [
        "روٙز"
    ],
    "hour": [
        "ساأت"
    ],
    "minute": [
        "دئیقە"
    ],
    "second": [
        "ثانیە"
    ],
    "relative-type": {
        "0 day ago": [
            "أمروٙ"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "this month"
        ],
        "0 second ago": [
            "now"
        ],
        "0 week ago": [
            "this week"
        ],
        "0 year ago": [
            "this year"
        ],
        "1 day ago": [
            "دیروٙز"
        ],
        "1 month ago": [
            "last month"
        ],
        "1 week ago": [
            "last week"
        ],
        "1 year ago": [
            "last year"
        ],
        "in 1 day": [
            "شوٙصوٙ"
        ],
        "in 1 month": [
            "next month"
        ],
        "in 1 week": [
            "next week"
        ],
        "in 1 year": [
            "next year"
        ]
    },
    "locale_specific": {
        "lrc-IQ": {
            "name": "lrc-IQ"
        }
    },
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
