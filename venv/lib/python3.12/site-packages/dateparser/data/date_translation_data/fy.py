info = {
    "name": "fy",
    "date_order": "DMY",
    "january": [
        "jan",
        "janne<PERSON>s"
    ],
    "february": [
        "feb",
        "febrewaris"
    ],
    "march": [
        "maart",
        "mrt"
    ],
    "april": [
        "apr",
        "april"
    ],
    "may": [
        "maaie",
        "mai"
    ],
    "june": [
        "jun",
        "juny"
    ],
    "july": [
        "jul",
        "july"
    ],
    "august": [
        "aug",
        "augustus"
    ],
    "september": [
        "sep",
        "septimber"
    ],
    "october": [
        "okt",
        "oktober"
    ],
    "november": [
        "nov",
        "novimber"
    ],
    "december": [
        "des",
        "desimber"
    ],
    "monday": [
        "mo",
        "moandei"
    ],
    "tuesday": [
        "ti",
        "tiisdei"
    ],
    "wednesday": [
        "wo",
        "woansdei"
    ],
    "thursday": [
        "to",
        "tongersdei"
    ],
    "friday": [
        "fr",
        "freed"
    ],
    "saturday": [
        "sneon",
        "so"
    ],
    "sunday": [
        "si",
        "snein"
    ],
    "am": [
        "am"
    ],
    "pm": [
        "pm"
    ],
    "year": [
        "jier"
    ],
    "month": [
        "moanne"
    ],
    "week": [
        "wike"
    ],
    "day": [
        "dei"
    ],
    "hour": [
        "oere"
    ],
    "minute": [
        "minút"
    ],
    "second": [
        "sekonde"
    ],
    "relative-type": {
        "0 day ago": [
            "vandaag"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "dizze moanne"
        ],
        "0 second ago": [
            "nu"
        ],
        "0 week ago": [
            "dizze wike"
        ],
        "0 year ago": [
            "dit jier"
        ],
        "1 day ago": [
            "gisteren"
        ],
        "1 month ago": [
            "foarige moanne"
        ],
        "1 week ago": [
            "foarige wike"
        ],
        "1 year ago": [
            "foarich jier"
        ],
        "in 1 day": [
            "morgen"
        ],
        "in 1 month": [
            "folgjende moanne"
        ],
        "in 1 week": [
            "folgjende wike"
        ],
        "in 1 year": [
            "folgjend jier"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "(\\d+[.,]?\\d*) dei lyn",
            "(\\d+[.,]?\\d*) deien lyn"
        ],
        "\\1 hour ago": [
            "(\\d+[.,]?\\d*) oere lyn"
        ],
        "\\1 minute ago": [
            "(\\d+[.,]?\\d*) minuten lyn",
            "(\\d+[.,]?\\d*) minút lyn"
        ],
        "\\1 month ago": [
            "(\\d+[.,]?\\d*) moanne lyn",
            "(\\d+[.,]?\\d*) moannen lyn"
        ],
        "\\1 second ago": [
            "(\\d+[.,]?\\d*) sekonde lyn",
            "(\\d+[.,]?\\d*) sekonden lyn"
        ],
        "\\1 week ago": [
            "(\\d+[.,]?\\d*) wike lyn",
            "(\\d+[.,]?\\d*) wiken lyn"
        ],
        "\\1 year ago": [
            "(\\d+[.,]?\\d*) jier lyn"
        ],
        "in \\1 day": [
            "oer (\\d+[.,]?\\d*) dei",
            "oer (\\d+[.,]?\\d*) deien"
        ],
        "in \\1 hour": [
            "oer (\\d+[.,]?\\d*) oere"
        ],
        "in \\1 minute": [
            "oer (\\d+[.,]?\\d*) minuten",
            "oer (\\d+[.,]?\\d*) minút"
        ],
        "in \\1 month": [
            "oer (\\d+[.,]?\\d*) moanne",
            "oer (\\d+[.,]?\\d*) moannen"
        ],
        "in \\1 second": [
            "oer (\\d+[.,]?\\d*) sekonde",
            "oer (\\d+[.,]?\\d*) sekonden"
        ],
        "in \\1 week": [
            "oer (\\d+[.,]?\\d*) wike",
            "oer (\\d+[.,]?\\d*) wiken"
        ],
        "in \\1 year": [
            "oer (\\d+[.,]?\\d*) jier"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
