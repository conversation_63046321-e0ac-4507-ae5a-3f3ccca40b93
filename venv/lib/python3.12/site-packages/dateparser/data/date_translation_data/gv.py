info = {
    "name": "gv",
    "date_order": "<PERSON><PERSON>",
    "january": [
        "j-guer",
        "jerrey-geuree"
    ],
    "february": [
        "t-arree",
        "toshiaght-arree"
    ],
    "march": [
        "mayrnt"
    ],
    "april": [
        "averil",
        "avrril"
    ],
    "may": [
        "boaldyn"
    ],
    "june": [
        "m-souree",
        "mean-souree"
    ],
    "july": [
        "j-souree",
        "jerrey-souree"
    ],
    "august": [
        "luanistyn"
    ],
    "september": [
        "m-fouyir",
        "mean-fouyir"
    ],
    "october": [
        "j-fouyir",
        "jerrey-fouyir"
    ],
    "november": [
        "m-houney",
        "mee houney"
    ],
    "december": [
        "m-nollick",
        "mee ny nollick"
    ],
    "monday": [
        "jel",
        "jelhein"
    ],
    "tuesday": [
        "jem",
        "jemayrt"
    ],
    "wednesday": [
        "jerc",
        "jercean"
    ],
    "thursday": [
        "jerd",
        "jerdein"
    ],
    "friday": [
        "jeh",
        "jeheiney"
    ],
    "saturday": [
        "jes",
        "jesarn"
    ],
    "sunday": [
        "jed",
        "jedoonee"
    ],
    "am": [
        "am"
    ],
    "pm": [
        "pm"
    ],
    "year": [
        "year"
    ],
    "month": [
        "month"
    ],
    "week": [
        "week"
    ],
    "day": [
        "day"
    ],
    "hour": [
        "hour"
    ],
    "minute": [
        "minute"
    ],
    "second": [
        "second"
    ],
    "relative-type": {
        "0 day ago": [
            "today"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "this month"
        ],
        "0 second ago": [
            "now"
        ],
        "0 week ago": [
            "this week"
        ],
        "0 year ago": [
            "this year"
        ],
        "1 day ago": [
            "yesterday"
        ],
        "1 month ago": [
            "last month"
        ],
        "1 week ago": [
            "last week"
        ],
        "1 year ago": [
            "last year"
        ],
        "in 1 day": [
            "tomorrow"
        ],
        "in 1 month": [
            "next month"
        ],
        "in 1 week": [
            "next week"
        ],
        "in 1 year": [
            "next year"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
