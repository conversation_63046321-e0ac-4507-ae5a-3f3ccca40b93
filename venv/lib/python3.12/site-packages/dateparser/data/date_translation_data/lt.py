info = {
    "name": "lt",
    "date_order": "<PERSON><PERSON>",
    "january": [
        "saus",
        "sausio",
        "sausis"
    ],
    "february": [
        "vas",
        "vasario",
        "vasaris"
    ],
    "march": [
        "kov",
        "kovas",
        "kovo"
    ],
    "april": [
        "bal",
        "balandis",
        "baland<PERSON><PERSON>"
    ],
    "may": [
        "geg",
        "gegu<PERSON><PERSON>",
        "gegu<PERSON><PERSON>s"
    ],
    "june": [
        "birž",
        "bir<PERSON><PERSON>o",
        "bir<PERSON>elis"
    ],
    "july": [
        "liep",
        "liepa",
        "liepos"
    ],
    "august": [
        "rugp",
        "rugpjūtis",
        "rugpjū<PERSON>io"
    ],
    "september": [
        "rugs",
        "rugsėjis",
        "rugsėjo"
    ],
    "october": [
        "spal",
        "spalio",
        "spalis"
    ],
    "november": [
        "lapkr",
        "lapkritis",
        "lapkri<PERSON>io"
    ],
    "december": [
        "gruod",
        "gruodis",
        "gruod<PERSON>io"
    ],
    "monday": [
        "pirmadienis",
        "pr"
    ],
    "tuesday": [
        "an",
        "antradienis"
    ],
    "wednesday": [
        "tr",
        "trečiadienis"
    ],
    "thursday": [
        "ketvirtadienis",
        "kt"
    ],
    "friday": [
        "penktadienis",
        "pn"
    ],
    "saturday": [
        "šeštadienis",
        "št"
    ],
    "sunday": [
        "sekmadienis",
        "sk"
    ],
    "am": [
        "priešpiet"
    ],
    "pm": [
        "popiet"
    ],
    "year": [
        "m",
        "metai"
    ],
    "month": [
        "mėn",
        "mėnuo"
    ],
    "week": [
        "sav",
        "savaitė"
    ],
    "day": [
        "d",
        "diena"
    ],
    "hour": [
        "h",
        "val",
        "valanda"
    ],
    "minute": [
        "min",
        "minutė"
    ],
    "second": [
        "s",
        "sek",
        "sekundė"
    ],
    "relative-type": {
        "0 day ago": [
            "šiandien"
        ],
        "0 hour ago": [
            "šią valandą"
        ],
        "0 minute ago": [
            "šią minutę"
        ],
        "0 month ago": [
            "šį mėnesį"
        ],
        "0 second ago": [
            "dabar"
        ],
        "0 week ago": [
            "šią savaitę"
        ],
        "0 year ago": [
            "šiais metais"
        ],
        "1 day ago": [
            "vakar"
        ],
        "1 month ago": [
            "praėjusį mėnesį"
        ],
        "1 week ago": [
            "praėjusią savaitę"
        ],
        "1 year ago": [
            "praėjusiais metais"
        ],
        "in 1 day": [
            "rytoj"
        ],
        "in 1 month": [
            "kitą mėnesį"
        ],
        "in 1 week": [
            "kitą savaitę"
        ],
        "in 1 year": [
            "kitais metais"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "prieš (\\d+[.,]?\\d*) d",
            "prieš (\\d+[.,]?\\d*) dieną",
            "prieš (\\d+[.,]?\\d*) dienų"
        ],
        "\\1 hour ago": [
            "prieš (\\d+[.,]?\\d*) val",
            "prieš (\\d+[.,]?\\d*) valandą",
            "prieš (\\d+[.,]?\\d*) valandų"
        ],
        "\\1 minute ago": [
            "prieš (\\d+[.,]?\\d*) min",
            "prieš (\\d+[.,]?\\d*) minutę",
            "prieš (\\d+[.,]?\\d*) minučių"
        ],
        "\\1 month ago": [
            "prieš (\\d+[.,]?\\d*) mėn",
            "prieš (\\d+[.,]?\\d*) mėnesių",
            "prieš (\\d+[.,]?\\d*) mėnesį"
        ],
        "\\1 second ago": [
            "prieš (\\d+[.,]?\\d*) s",
            "prieš (\\d+[.,]?\\d*) sek",
            "prieš (\\d+[.,]?\\d*) sekundę",
            "prieš (\\d+[.,]?\\d*) sekundžių"
        ],
        "\\1 week ago": [
            "prieš (\\d+[.,]?\\d*) sav",
            "prieš (\\d+[.,]?\\d*) savaitę",
            "prieš (\\d+[.,]?\\d*) savaičių"
        ],
        "\\1 year ago": [
            "prieš (\\d+[.,]?\\d*) m",
            "prieš (\\d+[.,]?\\d*) metus",
            "prieš (\\d+[.,]?\\d*) metų"
        ],
        "in \\1 day": [
            "po (\\d+[.,]?\\d*) d",
            "po (\\d+[.,]?\\d*) dienos",
            "po (\\d+[.,]?\\d*) dienų"
        ],
        "in \\1 hour": [
            "po (\\d+[.,]?\\d*) val",
            "po (\\d+[.,]?\\d*) valandos",
            "po (\\d+[.,]?\\d*) valandų"
        ],
        "in \\1 minute": [
            "po (\\d+[.,]?\\d*) min",
            "po (\\d+[.,]?\\d*) minutės",
            "po (\\d+[.,]?\\d*) minučių"
        ],
        "in \\1 month": [
            "po (\\d+[.,]?\\d*) mėn",
            "po (\\d+[.,]?\\d*) mėnesio",
            "po (\\d+[.,]?\\d*) mėnesių"
        ],
        "in \\1 second": [
            "po (\\d+[.,]?\\d*) s",
            "po (\\d+[.,]?\\d*) sek",
            "po (\\d+[.,]?\\d*) sekundės",
            "po (\\d+[.,]?\\d*) sekundžių"
        ],
        "in \\1 week": [
            "po (\\d+[.,]?\\d*) sav",
            "po (\\d+[.,]?\\d*) savaitės",
            "po (\\d+[.,]?\\d*) savaičių"
        ],
        "in \\1 year": [
            "po (\\d+[.,]?\\d*) m",
            "po (\\d+[.,]?\\d*) metų"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
