info = {
    "name": "kea",
    "date_order": "DMY",
    "january": [
        "jan",
        "janeru"
    ],
    "february": [
        "feb",
        "febreru"
    ],
    "march": [
        "mar",
        "marsu"
    ],
    "april": [
        "abr",
        "abril"
    ],
    "may": [
        "mai",
        "maiu"
    ],
    "june": [
        "jun",
        "junhu"
    ],
    "july": [
        "jul",
        "julhu"
    ],
    "august": [
        "ago",
        "agostu"
    ],
    "september": [
        "set",
        "setenbru"
    ],
    "october": [
        "otu",
        "otubru"
    ],
    "november": [
        "nuv",
        "nuvenbru"
    ],
    "december": [
        "diz",
        "dizenbru"
    ],
    "monday": [
        "sig",
        "sigunda-fera"
    ],
    "tuesday": [
        "ter",
        "tersa-fera"
    ],
    "wednesday": [
        "kua",
        "kuarta-fera"
    ],
    "thursday": [
        "kin",
        "kinta-fera"
    ],
    "friday": [
        "ses",
        "sesta-fera"
    ],
    "saturday": [
        "sab",
        "sabadu",
        "sábadu"
    ],
    "sunday": [
        "dum",
        "dumingu"
    ],
    "am": [
        "am"
    ],
    "pm": [
        "pm"
    ],
    "year": [
        "anu"
    ],
    "month": [
        "mes"
    ],
    "week": [
        "sim",
        "simana"
    ],
    "day": [
        "dia"
    ],
    "hour": [
        "h",
        "ora"
    ],
    "minute": [
        "m",
        "min",
        "minutu"
    ],
    "second": [
        "s",
        "sig",
        "sigundu"
    ],
    "relative-type": {
        "0 day ago": [
            "oji"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "es mes li"
        ],
        "0 second ago": [
            "now"
        ],
        "0 week ago": [
            "es simana li"
        ],
        "0 year ago": [
            "es anu li"
        ],
        "1 day ago": [
            "onti"
        ],
        "1 month ago": [
            "mes pasadu"
        ],
        "1 week ago": [
            "simana pasadu"
        ],
        "1 year ago": [
            "anu pasadu"
        ],
        "in 1 day": [
            "manha"
        ],
        "in 1 month": [
            "prósimu mes"
        ],
        "in 1 week": [
            "prósimu simana"
        ],
        "in 1 year": [
            "prósimu anu"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "a ten (\\d+[.,]?\\d*) dia"
        ],
        "\\1 hour ago": [
            "a ten (\\d+[.,]?\\d*) ora"
        ],
        "\\1 minute ago": [
            "a ten (\\d+[.,]?\\d*) m",
            "a ten (\\d+[.,]?\\d*) min",
            "a ten (\\d+[.,]?\\d*) minutu"
        ],
        "\\1 month ago": [
            "a ten (\\d+[.,]?\\d*) mes"
        ],
        "\\1 second ago": [
            "a ten (\\d+[.,]?\\d*) s",
            "a ten (\\d+[.,]?\\d*) sig",
            "a ten (\\d+[.,]?\\d*) sigundu"
        ],
        "\\1 week ago": [
            "a ten (\\d+[.,]?\\d*) sim",
            "a ten (\\d+[.,]?\\d*) simana"
        ],
        "\\1 year ago": [
            "a ten (\\d+[.,]?\\d*) anu"
        ],
        "in \\1 day": [
            "di li (\\d+[.,]?\\d*) dia"
        ],
        "in \\1 hour": [
            "di li (\\d+[.,]?\\d*) ora"
        ],
        "in \\1 minute": [
            "di li (\\d+[.,]?\\d*) m",
            "di li (\\d+[.,]?\\d*) min",
            "di li (\\d+[.,]?\\d*) minutu"
        ],
        "in \\1 month": [
            "di li (\\d+[.,]?\\d*) mes"
        ],
        "in \\1 second": [
            "di li (\\d+[.,]?\\d*) s",
            "di li (\\d+[.,]?\\d*) sig",
            "di li (\\d+[.,]?\\d*) sigundu"
        ],
        "in \\1 week": [
            "di li (\\d+[.,]?\\d*) sim",
            "di li (\\d+[.,]?\\d*) simana"
        ],
        "in \\1 year": [
            "di li (\\d+[.,]?\\d*) anu"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
