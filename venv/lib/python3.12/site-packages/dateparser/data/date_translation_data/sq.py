info = {
    "name": "sq",
    "date_order": "DM<PERSON>",
    "january": [
        "jan",
        "janar"
    ],
    "february": [
        "shk",
        "shkurt"
    ],
    "march": [
        "mar",
        "mars"
    ],
    "april": [
        "pri",
        "prill"
    ],
    "may": [
        "maj"
    ],
    "june": [
        "qer",
        "qershor"
    ],
    "july": [
        "kor",
        "korrik"
    ],
    "august": [
        "gsh",
        "gusht"
    ],
    "september": [
        "sht",
        "shtator"
    ],
    "october": [
        "tet",
        "tetor"
    ],
    "november": [
        "nën",
        "nëntor"
    ],
    "december": [
        "dhj",
        "dhjetor"
    ],
    "monday": [
        "e hënë",
        "hën"
    ],
    "tuesday": [
        "e martë",
        "mar"
    ],
    "wednesday": [
        "e mërkurë",
        "mër"
    ],
    "thursday": [
        "e enjte",
        "enj"
    ],
    "friday": [
        "e premte",
        "pre"
    ],
    "saturday": [
        "e shtunë",
        "sht"
    ],
    "sunday": [
        "die",
        "e diel"
    ],
    "am": [
        "e paradites",
        "paradite"
    ],
    "pm": [
        "e pasdites",
        "pasdite"
    ],
    "year": [
        "vit"
    ],
    "month": [
        "muaj"
    ],
    "week": [
        "javë"
    ],
    "day": [
        "ditë"
    ],
    "hour": [
        "orë"
    ],
    "minute": [
        "min",
        "minutë"
    ],
    "second": [
        "sek",
        "sekondë"
    ],
    "relative-type": {
        "0 day ago": [
            "sot"
        ],
        "0 hour ago": [
            "këtë orë"
        ],
        "0 minute ago": [
            "këtë minutë"
        ],
        "0 month ago": [
            "këtë muaj"
        ],
        "0 second ago": [
            "tani"
        ],
        "0 week ago": [
            "këtë javë"
        ],
        "0 year ago": [
            "këtë vit"
        ],
        "1 day ago": [
            "dje"
        ],
        "1 month ago": [
            "muajin e kaluar"
        ],
        "1 week ago": [
            "javën e kaluar"
        ],
        "1 year ago": [
            "vitin e kaluar"
        ],
        "in 1 day": [
            "nesër"
        ],
        "in 1 month": [
            "muajin e ardhshëm"
        ],
        "in 1 week": [
            "javën e ardhshme"
        ],
        "in 1 year": [
            "vitin e ardhshëm"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "(\\d+[.,]?\\d*) ditë më parë"
        ],
        "\\1 hour ago": [
            "(\\d+[.,]?\\d*) orë më parë"
        ],
        "\\1 minute ago": [
            "(\\d+[.,]?\\d*) min më parë",
            "(\\d+[.,]?\\d*) minuta më parë",
            "(\\d+[.,]?\\d*) minutë më parë"
        ],
        "\\1 month ago": [
            "(\\d+[.,]?\\d*) muaj më parë"
        ],
        "\\1 second ago": [
            "(\\d+[.,]?\\d*) sek më parë",
            "(\\d+[.,]?\\d*) sekonda më parë",
            "(\\d+[.,]?\\d*) sekondë më parë"
        ],
        "\\1 week ago": [
            "(\\d+[.,]?\\d*) javë më parë"
        ],
        "\\1 year ago": [
            "(\\d+[.,]?\\d*) vit më parë",
            "(\\d+[.,]?\\d*) vjet më parë"
        ],
        "in \\1 day": [
            "pas (\\d+[.,]?\\d*) dite",
            "pas (\\d+[.,]?\\d*) ditësh"
        ],
        "in \\1 hour": [
            "pas (\\d+[.,]?\\d*) ore",
            "pas (\\d+[.,]?\\d*) orësh"
        ],
        "in \\1 minute": [
            "pas (\\d+[.,]?\\d*) min",
            "pas (\\d+[.,]?\\d*) minutash",
            "pas (\\d+[.,]?\\d*) minute"
        ],
        "in \\1 month": [
            "pas (\\d+[.,]?\\d*) muaji",
            "pas (\\d+[.,]?\\d*) muajsh"
        ],
        "in \\1 second": [
            "pas (\\d+[.,]?\\d*) sek",
            "pas (\\d+[.,]?\\d*) sekondash",
            "pas (\\d+[.,]?\\d*) sekonde"
        ],
        "in \\1 week": [
            "pas (\\d+[.,]?\\d*) jave",
            "pas (\\d+[.,]?\\d*) javësh"
        ],
        "in \\1 year": [
            "pas (\\d+[.,]?\\d*) viti",
            "pas (\\d+[.,]?\\d*) vjetësh"
        ]
    },
    "locale_specific": {
        "sq-MK": {
            "name": "sq-MK"
        },
        "sq-XK": {
            "name": "sq-XK"
        }
    },
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
