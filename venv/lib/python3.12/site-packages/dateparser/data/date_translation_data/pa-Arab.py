info = {
    "name": "pa-Arab",
    "date_order": "DMY",
    "january": [
        "جنوری"
    ],
    "february": [
        "فروری"
    ],
    "march": [
        "مارچ"
    ],
    "april": [
        "اپریل"
    ],
    "may": [
        "مئ"
    ],
    "june": [
        "جون"
    ],
    "july": [
        "جولائی"
    ],
    "august": [
        "اگست"
    ],
    "september": [
        "ستمبر"
    ],
    "october": [
        "اکتوبر"
    ],
    "november": [
        "نومبر"
    ],
    "december": [
        "دسمبر"
    ],
    "monday": [
        "پیر"
    ],
    "tuesday": [
        "منگل"
    ],
    "wednesday": [
        "بُدھ"
    ],
    "thursday": [
        "جمعرات"
    ],
    "friday": [
        "جمعہ"
    ],
    "saturday": [
        "ہفتہ"
    ],
    "sunday": [
        "اتوار"
    ],
    "am": [
        "am"
    ],
    "pm": [
        "pm"
    ],
    "year": [
        "ورھا"
    ],
    "month": [
        "مہينا"
    ],
    "week": [
        "ہفتہ"
    ],
    "day": [
        "دئن"
    ],
    "hour": [
        "گھنٹا"
    ],
    "minute": [
        "منٹ"
    ],
    "second": [
        "second"
    ],
    "relative-type": {
        "0 day ago": [
            "today"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "this month"
        ],
        "0 second ago": [
            "now"
        ],
        "0 week ago": [
            "this week"
        ],
        "0 year ago": [
            "this year"
        ],
        "1 day ago": [
            "yesterday"
        ],
        "1 month ago": [
            "last month"
        ],
        "1 week ago": [
            "last week"
        ],
        "1 year ago": [
            "last year"
        ],
        "in 1 day": [
            "tomorrow"
        ],
        "in 1 month": [
            "next month"
        ],
        "in 1 week": [
            "next week"
        ],
        "in 1 year": [
            "next year"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
