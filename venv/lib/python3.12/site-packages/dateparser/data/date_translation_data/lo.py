info = {
    "name": "lo",
    "date_order": "DMY",
    "january": [
        "ມກ",
        "ມັງກອນ"
    ],
    "february": [
        "ກພ",
        "ກຸມພາ"
    ],
    "march": [
        "ມນ",
        "ມີນາ"
    ],
    "april": [
        "ມສ",
        "ເມສາ"
    ],
    "may": [
        "ພພ",
        "ພຶດສະພາ"
    ],
    "june": [
        "ມິຖ",
        "ມິຖຸນາ"
    ],
    "july": [
        "ກລ",
        "ກໍລະກົດ"
    ],
    "august": [
        "ສຫ",
        "ສິງຫາ"
    ],
    "september": [
        "ກຍ",
        "ກັນຍາ"
    ],
    "october": [
        "ຕລ",
        "ຕຸລາ"
    ],
    "november": [
        "ພຈ",
        "ພະຈິກ"
    ],
    "december": [
        "ທວ",
        "ທັນວາ"
    ],
    "monday": [
        "ຈັນ",
        "ວັນຈັນ"
    ],
    "tuesday": [
        "ວັນອັງຄານ",
        "ອັງຄານ"
    ],
    "wednesday": [
        "ພຸດ",
        "ວັນພຸດ"
    ],
    "thursday": [
        "ພະຫັດ",
        "ວັນພະຫັດ"
    ],
    "friday": [
        "ວັນສຸກ",
        "ສຸກ"
    ],
    "saturday": [
        "ວັນເສົາ",
        "ເສົາ"
    ],
    "sunday": [
        "ວັນອາທິດ",
        "ອາທິດ"
    ],
    "am": [
        "ກ່ອນທ່ຽງ"
    ],
    "pm": [
        "ຫຼັງທ່ຽງ"
    ],
    "year": [
        "ປີ"
    ],
    "month": [
        "ດ",
        "ເດືອນ"
    ],
    "week": [
        "ອ",
        "ອາທິດ"
    ],
    "day": [
        "ມື້"
    ],
    "hour": [
        "ຊມ",
        "ຊົ່ວໂມງ"
    ],
    "minute": [
        "ນທ",
        "ນາທີ"
    ],
    "second": [
        "ວິ",
        "ວິນາທີ"
    ],
    "relative-type": {
        "0 day ago": [
            "ມື້ນີ້"
        ],
        "0 hour ago": [
            "ຊົ່ວໂມງນີ້"
        ],
        "0 minute ago": [
            "ນາທີນີ້"
        ],
        "0 month ago": [
            "ເດືອນນີ້"
        ],
        "0 second ago": [
            "ຕອນນີ້"
        ],
        "0 week ago": [
            "ອາທິດນີ້"
        ],
        "0 year ago": [
            "ປີນີ້"
        ],
        "1 day ago": [
            "ມື້ວານ"
        ],
        "1 month ago": [
            "ເດືອນແລ້ວ"
        ],
        "1 week ago": [
            "ອາທິດແລ້ວ"
        ],
        "1 year ago": [
            "ປີກາຍ"
        ],
        "in 1 day": [
            "ມື້ອື່ນ"
        ],
        "in 1 month": [
            "ເດືອນໜ້າ"
        ],
        "in 1 week": [
            "ອາທິດໜ້າ"
        ],
        "in 1 year": [
            "ປີໜ້າ"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "(\\d+[.,]?\\d*) ມື້ກ່ອນ"
        ],
        "\\1 hour ago": [
            "(\\d+[.,]?\\d*) ຊມ ກ່ອນ",
            "(\\d+[.,]?\\d*) ຊົ່ວໂມງກ່ອນ"
        ],
        "\\1 minute ago": [
            "(\\d+[.,]?\\d*) ນທ ກ່ອນ",
            "(\\d+[.,]?\\d*) ນາທີກ່ອນ"
        ],
        "\\1 month ago": [
            "(\\d+[.,]?\\d*) ດ ກ່ອນ",
            "(\\d+[.,]?\\d*) ເດືອນກ່ອນ"
        ],
        "\\1 second ago": [
            "(\\d+[.,]?\\d*) ວິ ກ່ອນ",
            "(\\d+[.,]?\\d*) ວິນາທີກ່ອນ"
        ],
        "\\1 week ago": [
            "(\\d+[.,]?\\d*) ອທ ກ່ອນ",
            "(\\d+[.,]?\\d*) ອາທິດກ່ອນ"
        ],
        "\\1 year ago": [
            "(\\d+[.,]?\\d*) ປີກ່ອນ"
        ],
        "in \\1 day": [
            "ໃນອີກ (\\d+[.,]?\\d*) ມື້"
        ],
        "in \\1 hour": [
            "ໃນອີກ (\\d+[.,]?\\d*) ຊມ",
            "ໃນອີກ (\\d+[.,]?\\d*) ຊົ່ວໂມງ"
        ],
        "in \\1 minute": [
            "(\\d+[.,]?\\d*) ໃນອີກ 0 ນາທີ",
            "ໃນ (\\d+[.,]?\\d*) ນທ"
        ],
        "in \\1 month": [
            "ໃນອີກ (\\d+[.,]?\\d*) ດ",
            "ໃນອີກ (\\d+[.,]?\\d*) ເດືອນ"
        ],
        "in \\1 second": [
            "ໃນ (\\d+[.,]?\\d*) ວິ",
            "ໃນອີກ (\\d+[.,]?\\d*) ວິນາທີ"
        ],
        "in \\1 week": [
            "ໃນອີກ (\\d+[.,]?\\d*) ອທ",
            "ໃນອີກ (\\d+[.,]?\\d*) ອາທິດ"
        ],
        "in \\1 year": [
            "ໃນອີກ (\\d+[.,]?\\d*) ປີ"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
