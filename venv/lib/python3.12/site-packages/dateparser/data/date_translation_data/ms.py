info = {
    "name": "ms",
    "date_order": "DMY",
    "january": [
        "jan",
        "januari"
    ],
    "february": [
        "feb",
        "februari"
    ],
    "march": [
        "mac"
    ],
    "april": [
        "apr",
        "april"
    ],
    "may": [
        "mei"
    ],
    "june": [
        "jun"
    ],
    "july": [
        "jul",
        "julai"
    ],
    "august": [
        "ogo",
        "ogos"
    ],
    "september": [
        "sep",
        "september"
    ],
    "october": [
        "okt",
        "oktober"
    ],
    "november": [
        "nov",
        "november"
    ],
    "december": [
        "dis",
        "disember"
    ],
    "monday": [
        "isn",
        "isnin"
    ],
    "tuesday": [
        "sel",
        "selasa"
    ],
    "wednesday": [
        "rab",
        "rabu"
    ],
    "thursday": [
        "kha",
        "khamis"
    ],
    "friday": [
        "jum",
        "jumaat"
    ],
    "saturday": [
        "sab",
        "sabtu"
    ],
    "sunday": [
        "ahad",
        "ahd"
    ],
    "am": [
        "pg"
    ],
    "pm": [
        "ptg"
    ],
    "year": [
        "tahun",
        "thn"
    ],
    "month": [
        "bln",
        "bulan"
    ],
    "week": [
        "mgu",
        "minggu"
    ],
    "day": [
        "hari"
    ],
    "hour": [
        "jam"
    ],
    "minute": [
        "min",
        "minit"
    ],
    "second": [
        "saat"
    ],
    "relative-type": {
        "0 day ago": [
            "hari ini"
        ],
        "0 hour ago": [
            "jam ini"
        ],
        "0 minute ago": [
            "pada minit ini"
        ],
        "0 month ago": [
            "bln ini",
            "bulan ini"
        ],
        "0 second ago": [
            "sekarang"
        ],
        "0 week ago": [
            "minggu ini",
            "mng ini"
        ],
        "0 year ago": [
            "tahun ini",
            "thn ini"
        ],
        "1 day ago": [
            "semalam",
            "semlm"
        ],
        "1 month ago": [
            "bln lalu",
            "bulan lalu"
        ],
        "1 week ago": [
            "minggu lalu",
            "mng lepas"
        ],
        "1 year ago": [
            "tahun lalu",
            "thn lepas"
        ],
        "in 1 day": [
            "esok"
        ],
        "in 1 month": [
            "bln depan",
            "bulan depan"
        ],
        "in 1 week": [
            "minggu depan",
            "mng depan"
        ],
        "in 1 year": [
            "tahun depan",
            "thn depan"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "(\\d+[.,]?\\d*) hari lalu"
        ],
        "\\1 hour ago": [
            "(\\d+[.,]?\\d*) jam lalu"
        ],
        "\\1 minute ago": [
            "(\\d+[.,]?\\d*) min lalu",
            "(\\d+[.,]?\\d*) minit lalu"
        ],
        "\\1 month ago": [
            "(\\d+[.,]?\\d*) bln lalu",
            "(\\d+[.,]?\\d*) bulan lalu"
        ],
        "\\1 second ago": [
            "(\\d+[.,]?\\d*) saat lalu"
        ],
        "\\1 week ago": [
            "(\\d+[.,]?\\d*) mgu lalu",
            "(\\d+[.,]?\\d*) minggu lalu"
        ],
        "\\1 year ago": [
            "(\\d+[.,]?\\d*) tahun lalu",
            "(\\d+[.,]?\\d*) thn lalu"
        ],
        "in \\1 day": [
            "dalam (\\d+[.,]?\\d*) hari",
            "dlm (\\d+[.,]?\\d*) hari"
        ],
        "in \\1 hour": [
            "dalam (\\d+[.,]?\\d*) jam",
            "dlm (\\d+[.,]?\\d*) jam"
        ],
        "in \\1 minute": [
            "dalam (\\d+[.,]?\\d*) minit",
            "dlm (\\d+[.,]?\\d*) min"
        ],
        "in \\1 month": [
            "dalam (\\d+[.,]?\\d*) bulan",
            "dlm (\\d+[.,]?\\d*) bln"
        ],
        "in \\1 second": [
            "dalam (\\d+[.,]?\\d*) saat",
            "dlm (\\d+[.,]?\\d*) saat"
        ],
        "in \\1 week": [
            "dalam (\\d+[.,]?\\d*) minggu",
            "dlm (\\d+[.,]?\\d*) mgu"
        ],
        "in \\1 year": [
            "dalam (\\d+[.,]?\\d*) saat",
            "dalam (\\d+[.,]?\\d*) thn"
        ]
    },
    "locale_specific": {
        "ms-BN": {
            "name": "ms-BN"
        },
        "ms-SG": {
            "name": "ms-SG"
        }
    },
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
