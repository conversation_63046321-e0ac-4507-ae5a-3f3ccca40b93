#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Binance API 測試和診斷工具
"""

import os
from binance.client import Client
from dotenv import load_dotenv
import requests

def test_api_connection():
    """測試API連接和權限"""
    print("🔍 Binance API 診斷工具")
    print("=" * 50)
    
    # 載入環境變數
    load_dotenv()
    api_key = os.getenv('API_KEY')
    api_secret = os.getenv('API_SECRET')
    
    print(f"📋 API Key: {api_key[:10]}...{api_key[-10:] if api_key else 'None'}")
    print(f"📋 API Secret: {'已設定' if api_secret else '未設定'}")
    
    if not api_key or not api_secret:
        print("❌ API金鑰或密鑰未設定")
        return False
    
    # 測試基本連接
    print("\n🌐 測試網路連接...")
    try:
        response = requests.get("https://api.binance.com/api/v3/ping", timeout=10)
        if response.status_code == 200:
            print("✅ Binance API 網路連接正常")
        else:
            print(f"❌ 網路連接異常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 網路連接失敗: {e}")
        return False
    
    # 初始化客戶端
    print("\n🔑 測試API金鑰...")
    try:
        client = Client(api_key, api_secret)
        
        # 測試現貨帳戶權限
        print("📊 測試現貨帳戶權限...")
        try:
            account_info = client.get_account()
            print("✅ 現貨帳戶權限正常")
            print(f"   帳戶類型: {account_info.get('accountType', 'Unknown')}")
        except Exception as e:
            print(f"❌ 現貨帳戶權限失敗: {e}")
        
        # 測試合約帳戶權限
        print("📈 測試合約帳戶權限...")
        try:
            futures_account = client.futures_account()
            print("✅ 合約帳戶權限正常")
            print(f"   總餘額: {futures_account.get('totalWalletBalance', 'Unknown')} USDT")
            return True
        except Exception as e:
            print(f"❌ 合約帳戶權限失敗: {e}")
            print("\n💡 可能的解決方案:")
            print("   1. 確認API金鑰是否正確")
            print("   2. 檢查API金鑰是否啟用了合約交易權限")
            print("   3. 確認IP白名單設定（如果有設定的話）")
            print("   4. 檢查API金鑰是否已過期")
            return False
            
    except Exception as e:
        print(f"❌ API客戶端初始化失敗: {e}")
        return False

def show_api_permissions_guide():
    """顯示API權限設定指南"""
    print("\n" + "=" * 60)
    print("📖 Binance API 權限設定指南")
    print("=" * 60)
    print("\n🔧 如何設定API權限:")
    print("1. 登入 Binance 帳戶")
    print("2. 前往 [帳戶] > [API管理]")
    print("3. 建立新的API金鑰或編輯現有的")
    print("4. 確保啟用以下權限:")
    print("   ✓ 讀取資訊 (Read Info)")
    print("   ✓ 合約交易 (Futures Trading) - 用於讀取合約帳戶資訊")
    print("5. 如果有設定IP白名單，請加入當前IP: ***********")
    print("\n⚠️  注意事項:")
    print("- 本程式只需要讀取權限，不會進行任何交易")
    print("- 建議為此分析工具建立專用的API金鑰")
    print("- 確保API金鑰的安全性，不要分享給他人")

if __name__ == "__main__":
    success = test_api_connection()
    if not success:
        show_api_permissions_guide()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 API測試成功！您可以執行 main.py 來進行完整分析")
    else:
        print("❌ API測試失敗，請根據上述指南檢查設定")
    print("=" * 60)
