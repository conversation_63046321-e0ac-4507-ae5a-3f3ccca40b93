#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Binance 合約交易紀錄分析器
用於調取完整的合約交易歷史並進行損益統計分析
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from binance.client import Client
from binance.exceptions import BinanceAPIException
from dotenv import load_dotenv
import json
import time
import logging

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('binance_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BinanceFuturesAnalyzer:
    def __init__(self):
        """初始化分析器"""
        load_dotenv()
        
        # 從環境變數讀取API金鑰
        api_key = os.getenv('API_KEY')
        api_secret = os.getenv('API_SECRET')
        
        if not api_key or not api_secret:
            raise ValueError("請確保 .env 檔案中包含有效的 API_KEY 和 API_SECRET")
        
        # 初始化Binance客戶端
        self.client = Client(api_key, api_secret)
        
        # 測試連接
        try:
            account_info = self.client.futures_account()
            logger.info("成功連接到Binance API")
            logger.info(f"帳戶餘額: {account_info['totalWalletBalance']} USDT")
        except Exception as e:
            logger.error(f"API連接失敗: {e}")
            raise
    
    def get_all_futures_trades(self, symbol=None, start_time=None, limit=1000):
        """
        獲取所有合約交易紀錄
        
        Args:
            symbol: 交易對符號，如果為None則獲取所有交易對
            start_time: 開始時間戳
            limit: 每次請求的限制數量
        """
        all_trades = []
        
        try:
            # 如果沒有指定交易對，先獲取所有交易對
            if symbol is None:
                exchange_info = self.client.futures_exchange_info()
                symbols = [s['symbol'] for s in exchange_info['symbols'] if s['status'] == 'TRADING']
                logger.info(f"找到 {len(symbols)} 個活躍交易對")
            else:
                symbols = [symbol]
            
            for sym in symbols:
                logger.info(f"正在獲取 {sym} 的交易紀錄...")
                
                try:
                    # 獲取該交易對的所有交易
                    trades = self.client.futures_account_trades(symbol=sym, limit=limit)
                    
                    if trades:
                        logger.info(f"{sym}: 找到 {len(trades)} 筆交易")
                        all_trades.extend(trades)
                    
                    # API限制，稍作延遲
                    time.sleep(0.1)
                    
                except BinanceAPIException as e:
                    if e.code == -2013:  # No such symbol
                        continue
                    logger.warning(f"獲取 {sym} 交易紀錄時出錯: {e}")
                    continue
                
        except Exception as e:
            logger.error(f"獲取交易紀錄時出錯: {e}")
            raise
        
        logger.info(f"總共獲取到 {len(all_trades)} 筆交易紀錄")
        return all_trades
    
    def get_futures_income_history(self, income_type=None, start_time=None, end_time=None):
        """
        獲取合約收入歷史（包含已實現盈虧、手續費等）
        
        Args:
            income_type: 收入類型 (REALIZED_PNL, COMMISSION, etc.)
            start_time: 開始時間戳
            end_time: 結束時間戳
        """
        all_income = []
        
        try:
            # 獲取收入歷史
            income_history = self.client.futures_income_history(
                incomeType=income_type,
                startTime=start_time,
                endTime=end_time,
                limit=1000
            )
            
            all_income.extend(income_history)
            logger.info(f"獲取到 {len(income_history)} 筆收入紀錄")
            
        except Exception as e:
            logger.error(f"獲取收入歷史時出錯: {e}")
            raise
        
        return all_income
    
    def analyze_trades(self, trades_data):
        """分析交易數據"""
        if not trades_data:
            logger.warning("沒有交易數據可供分析")
            return None
        
        # 轉換為DataFrame
        df = pd.DataFrame(trades_data)
        
        # 轉換數據類型
        df['price'] = pd.to_numeric(df['price'])
        df['qty'] = pd.to_numeric(df['qty'])
        df['quoteQty'] = pd.to_numeric(df['quoteQty'])
        df['realizedPnl'] = pd.to_numeric(df['realizedPnl'])
        df['commission'] = pd.to_numeric(df['commission'])
        df['time'] = pd.to_datetime(df['time'], unit='ms')
        
        # 基本統計
        total_trades = len(df)
        total_volume = df['quoteQty'].sum()
        total_pnl = df['realizedPnl'].sum()
        total_commission = df['commission'].sum()
        
        # 按交易對分組統計
        symbol_stats = df.groupby('symbol').agg({
            'realizedPnl': ['sum', 'count'],
            'commission': 'sum',
            'quoteQty': 'sum'
        }).round(4)
        
        # 按日期分組統計
        df['date'] = df['time'].dt.date
        daily_stats = df.groupby('date').agg({
            'realizedPnl': 'sum',
            'commission': 'sum',
            'quoteQty': 'sum'
        }).round(4)
        
        analysis_result = {
            'summary': {
                'total_trades': total_trades,
                'total_volume_usdt': round(total_volume, 4),
                'total_realized_pnl': round(total_pnl, 4),
                'total_commission': round(total_commission, 4),
                'net_pnl': round(total_pnl - total_commission, 4),
                'first_trade': df['time'].min(),
                'last_trade': df['time'].max()
            },
            'symbol_stats': symbol_stats,
            'daily_stats': daily_stats,
            'raw_data': df
        }
        
        return analysis_result

    def generate_loss_report(self, analysis_result):
        """生成虧損報告"""
        if not analysis_result:
            return None

        df = analysis_result['raw_data']
        summary = analysis_result['summary']

        # 虧損交易統計
        loss_trades = df[df['realizedPnl'] < 0]
        profit_trades = df[df['realizedPnl'] > 0]

        loss_report = {
            'total_loss_amount': round(loss_trades['realizedPnl'].sum(), 4),
            'total_profit_amount': round(profit_trades['realizedPnl'].sum(), 4),
            'loss_trades_count': len(loss_trades),
            'profit_trades_count': len(profit_trades),
            'loss_percentage': round((len(loss_trades) / len(df)) * 100, 2) if len(df) > 0 else 0,
            'average_loss': round(loss_trades['realizedPnl'].mean(), 4) if len(loss_trades) > 0 else 0,
            'average_profit': round(profit_trades['realizedPnl'].mean(), 4) if len(profit_trades) > 0 else 0,
            'largest_loss': round(loss_trades['realizedPnl'].min(), 4) if len(loss_trades) > 0 else 0,
            'largest_profit': round(profit_trades['realizedPnl'].max(), 4) if len(profit_trades) > 0 else 0
        }

        # 按交易對的虧損統計
        symbol_loss = df.groupby('symbol')['realizedPnl'].agg(['sum', 'count', 'mean']).round(4)
        symbol_loss = symbol_loss[symbol_loss['sum'] < 0].sort_values('sum')

        loss_report['symbol_losses'] = symbol_loss
        loss_report['top_loss_symbols'] = symbol_loss.head(10)

        return loss_report

    def save_to_excel(self, analysis_result, loss_report, filename='binance_futures_analysis.xlsx'):
        """將分析結果保存到Excel檔案"""
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 摘要頁
                summary_df = pd.DataFrame([analysis_result['summary']]).T
                summary_df.columns = ['數值']
                summary_df.to_excel(writer, sheet_name='交易摘要', index_label='項目')

                # 虧損報告頁
                if loss_report:
                    loss_summary = {k: v for k, v in loss_report.items()
                                  if not isinstance(v, pd.DataFrame)}
                    loss_df = pd.DataFrame([loss_summary]).T
                    loss_df.columns = ['數值']
                    loss_df.to_excel(writer, sheet_name='虧損分析', index_label='項目')

                    # 交易對虧損明細
                    if 'symbol_losses' in loss_report and not loss_report['symbol_losses'].empty:
                        loss_report['symbol_losses'].to_excel(writer, sheet_name='交易對虧損明細')

                # 交易對統計
                analysis_result['symbol_stats'].to_excel(writer, sheet_name='交易對統計')

                # 每日統計
                analysis_result['daily_stats'].to_excel(writer, sheet_name='每日統計')

                # 原始交易數據
                analysis_result['raw_data'].to_excel(writer, sheet_name='原始交易數據', index=False)

            logger.info(f"分析結果已保存到 {filename}")

        except Exception as e:
            logger.error(f"保存Excel檔案時出錯: {e}")
            raise

    def print_summary_report(self, analysis_result, loss_report):
        """列印摘要報告"""
        print("\n" + "="*80)
        print("BINANCE 合約交易分析報告")
        print("="*80)

        if analysis_result:
            summary = analysis_result['summary']
            print(f"\n📊 交易摘要:")
            print(f"   總交易筆數: {summary['total_trades']:,}")
            print(f"   總交易量: {summary['total_volume_usdt']:,.4f} USDT")
            print(f"   總已實現盈虧: {summary['total_realized_pnl']:,.4f} USDT")
            print(f"   總手續費: {summary['total_commission']:,.4f} USDT")
            print(f"   淨盈虧: {summary['net_pnl']:,.4f} USDT")
            print(f"   交易期間: {summary['first_trade']} 至 {summary['last_trade']}")

        if loss_report:
            print(f"\n💸 虧損分析:")
            print(f"   總虧損金額: {loss_report['total_loss_amount']:,.4f} USDT")
            print(f"   總盈利金額: {loss_report['total_profit_amount']:,.4f} USDT")
            print(f"   虧損交易數: {loss_report['loss_trades_count']:,}")
            print(f"   盈利交易數: {loss_report['profit_trades_count']:,}")
            print(f"   虧損交易比例: {loss_report['loss_percentage']:.2f}%")
            print(f"   平均虧損: {loss_report['average_loss']:,.4f} USDT")
            print(f"   平均盈利: {loss_report['average_profit']:,.4f} USDT")
            print(f"   最大單筆虧損: {loss_report['largest_loss']:,.4f} USDT")
            print(f"   最大單筆盈利: {loss_report['largest_profit']:,.4f} USDT")

            if 'top_loss_symbols' in loss_report and not loss_report['top_loss_symbols'].empty:
                print(f"\n🔻 虧損最多的交易對 (前10名):")
                for symbol, data in loss_report['top_loss_symbols'].iterrows():
                    print(f"   {symbol}: {data['sum']:,.4f} USDT ({data['count']} 筆交易)")

        print("\n" + "="*80)
