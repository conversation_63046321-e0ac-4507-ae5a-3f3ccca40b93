#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
入金記錄分析器
統整所有入金記錄並進行詳細統計
"""

import pandas as pd
import numpy as np
from datetime import datetime
import glob
from binance_account_analyzer import BinanceAccountAnalyzer

class DepositAnalyzer:
    def __init__(self):
        """初始化入金分析器"""
        self.analyzer = BinanceAccountAnalyzer()
        
    def analyze_all_deposits(self):
        """分析所有入金記錄"""
        # 載入數據
        if not self.analyzer.load_csv_files():
            return None
        
        # 獲取所有數據
        all_data = self.analyzer.all_data
        
        if all_data is None or all_data.empty:
            print("❌ 沒有數據")
            return None
        
        print(f"📊 總數據量: {len(all_data):,} 筆記錄")
        
        # 篩選入金相關記錄
        deposit_operations = [
            'Deposit',           # 存款
            'Fiat Deposit',      # 法幣存款
            'Card Deposit',      # 信用卡存款
            'P2P Buy',           # P2P購買
            'Buy',               # 購買
            'Transfer Between Main and Funding Wallet',  # 錢包間轉帳
            'Transfer Between Main and Trading Account', # 主帳戶轉帳
            'Transfer Between Spot Account and USD-M Futures Account', # 現貨到合約轉帳
            'Transfer Between USD-M Futures Account and Spot Account', # 合約到現貨轉帳
        ]
        
        # 篩選入金記錄（Change > 0 表示資金流入）
        deposit_data = all_data[
            (all_data['Operation'].isin(deposit_operations)) & 
            (all_data['Change'] > 0)
        ].copy()
        
        print(f"📈 入金相關記錄: {len(deposit_data):,} 筆")
        
        if deposit_data.empty:
            print("❌ 沒有找到入金記錄")
            return None
        
        # 分析不同類型的入金
        analysis_result = {
            'all_deposits': self.analyze_deposit_summary(deposit_data),
            'by_operation': self.analyze_by_operation_type(deposit_data),
            'by_coin': self.analyze_by_coin(deposit_data),
            'by_time': self.analyze_by_time(deposit_data),
            'by_account': self.analyze_by_account(deposit_data),
            'raw_data': deposit_data
        }
        
        return analysis_result
    
    def analyze_deposit_summary(self, deposit_data):
        """分析入金總結"""
        print("\n📊 正在分析入金總結...")
        
        # 基本統計
        total_deposits = len(deposit_data)
        total_amount = deposit_data['Change'].sum()
        first_deposit = deposit_data['UTC_Time'].min()
        last_deposit = deposit_data['UTC_Time'].max()
        deposit_days = (last_deposit - first_deposit).days
        
        # 按幣種統計
        coin_summary = deposit_data.groupby('Coin')['Change'].agg([
            'sum', 'count', 'mean', 'max', 'min'
        ]).round(4)
        coin_summary.columns = ['總金額', '次數', '平均金額', '最大金額', '最小金額']
        coin_summary = coin_summary.sort_values('總金額', ascending=False)
        
        summary = {
            'total_deposits': total_deposits,
            'total_amount': round(total_amount, 4),
            'first_deposit': first_deposit,
            'last_deposit': last_deposit,
            'deposit_days': deposit_days,
            'average_amount': round(deposit_data['Change'].mean(), 4),
            'largest_deposit': round(deposit_data['Change'].max(), 4),
            'smallest_deposit': round(deposit_data['Change'].min(), 4),
            'coin_summary': coin_summary
        }
        
        return summary
    
    def analyze_by_operation_type(self, deposit_data):
        """按操作類型分析入金"""
        print("🔍 正在按操作類型分析...")
        
        operation_stats = deposit_data.groupby('Operation').agg({
            'Change': ['sum', 'count', 'mean'],
            'UTC_Time': ['min', 'max']
        }).round(4)
        
        operation_stats.columns = ['總金額', '次數', '平均金額', '首次時間', '最後時間']
        operation_stats = operation_stats.sort_values('總金額', ascending=False)
        
        return operation_stats
    
    def analyze_by_coin(self, deposit_data):
        """按幣種分析入金"""
        print("🪙 正在按幣種分析...")
        
        coin_stats = deposit_data.groupby('Coin').agg({
            'Change': ['sum', 'count', 'mean', 'max', 'min'],
            'UTC_Time': ['min', 'max']
        }).round(4)
        
        coin_stats.columns = ['總金額', '次數', '平均金額', '最大金額', '最小金額', '首次時間', '最後時間']
        coin_stats = coin_stats.sort_values('總金額', ascending=False)
        
        return coin_stats
    
    def analyze_by_time(self, deposit_data):
        """按時間分析入金"""
        print("📅 正在按時間分析...")
        
        # 按年分析
        deposit_data['年份'] = deposit_data['UTC_Time'].dt.year
        yearly_stats = deposit_data.groupby('年份').agg({
            'Change': ['sum', 'count', 'mean'],
            'Coin': lambda x: x.nunique()
        }).round(4)
        yearly_stats.columns = ['總金額', '次數', '平均金額', '幣種數']
        
        # 按月分析
        deposit_data['年月'] = deposit_data['UTC_Time'].dt.to_period('M')
        monthly_stats = deposit_data.groupby('年月').agg({
            'Change': ['sum', 'count', 'mean'],
            'Coin': lambda x: x.nunique()
        }).round(4)
        monthly_stats.columns = ['總金額', '次數', '平均金額', '幣種數']
        monthly_stats = monthly_stats.sort_values('總金額', ascending=False)
        
        return {
            'yearly': yearly_stats,
            'monthly': monthly_stats
        }
    
    def analyze_by_account(self, deposit_data):
        """按帳戶類型分析入金"""
        print("🏦 正在按帳戶類型分析...")
        
        account_stats = deposit_data.groupby('Account').agg({
            'Change': ['sum', 'count', 'mean'],
            'Coin': lambda x: x.nunique(),
            'UTC_Time': ['min', 'max']
        }).round(4)
        
        account_stats.columns = ['總金額', '次數', '平均金額', '幣種數', '首次時間', '最後時間']
        account_stats = account_stats.sort_values('總金額', ascending=False)
        
        return account_stats

def print_deposit_report(analysis_result):
    """列印入金分析報告"""
    print("\n" + "="*120)
    print("💰 完整入金記錄分析報告")
    print("="*120)
    
    summary = analysis_result['all_deposits']
    
    # 1. 總結
    print(f"\n📊 入金總結:")
    print(f"   總入金次數: {summary['total_deposits']:,}")
    print(f"   總入金金額: {summary['total_amount']:,.4f}")
    print(f"   入金期間: {summary['first_deposit']} 至 {summary['last_deposit']}")
    print(f"   入金天數: {summary['deposit_days']:,} 天")
    print(f"   平均入金: {summary['average_amount']:,.4f}")
    print(f"   最大入金: {summary['largest_deposit']:,.4f}")
    print(f"   最小入金: {summary['smallest_deposit']:,.4f}")
    
    # 2. 按操作類型
    print(f"\n🔍 按操作類型統計:")
    print("-" * 80)
    print(f"{'操作類型':30s} {'總金額':>15s} {'次數':>8s} {'平均金額':>12s}")
    print("-" * 80)
    
    operation_stats = analysis_result['by_operation']
    for operation, data in operation_stats.head(10).iterrows():
        print(f"{operation[:29]:30s} {data['總金額']:>15,.2f} {data['次數']:>8,.0f} {data['平均金額']:>12,.2f}")
    
    # 3. 按幣種統計
    print(f"\n🪙 按幣種統計 (前15名):")
    print("-" * 80)
    print(f"{'幣種':8s} {'總金額':>15s} {'次數':>8s} {'平均金額':>12s} {'最大金額':>12s}")
    print("-" * 80)
    
    coin_stats = analysis_result['by_coin']
    for coin, data in coin_stats.head(15).iterrows():
        print(f"{coin:8s} {data['總金額']:>15,.2f} {data['次數']:>8,.0f} {data['平均金額']:>12,.2f} {data['最大金額']:>12,.2f}")
    
    # 4. 按年度統計
    print(f"\n📅 按年度統計:")
    print("-" * 60)
    print(f"{'年份':6s} {'總金額':>15s} {'次數':>8s} {'平均金額':>12s} {'幣種數':>8s}")
    print("-" * 60)
    
    yearly_stats = analysis_result['by_time']['yearly']
    for year, data in yearly_stats.iterrows():
        print(f"{year:6d} {data['總金額']:>15,.2f} {data['次數']:>8,.0f} {data['平均金額']:>12,.2f} {data['幣種數']:>8,.0f}")
    
    # 5. 按帳戶類型統計
    print(f"\n🏦 按帳戶類型統計:")
    print("-" * 80)
    print(f"{'帳戶類型':25s} {'總金額':>15s} {'次數':>8s} {'平均金額':>12s} {'幣種數':>8s}")
    print("-" * 80)
    
    account_stats = analysis_result['by_account']
    for account, data in account_stats.iterrows():
        print(f"{account[:24]:25s} {data['總金額']:>15,.2f} {data['次數']:>8,.0f} {data['平均金額']:>12,.2f} {data['幣種數']:>8,.0f}")
    
    # 6. 入金最多的月份
    print(f"\n📈 入金最多的月份 (前10名):")
    print("-" * 60)
    print(f"{'年月':8s} {'總金額':>15s} {'次數':>8s} {'平均金額':>12s}")
    print("-" * 60)
    
    monthly_stats = analysis_result['by_time']['monthly']
    for month, data in monthly_stats.head(10).iterrows():
        print(f"{str(month):8s} {data['總金額']:>15,.2f} {data['次數']:>8,.0f} {data['平均金額']:>12,.2f}")
    
    print("\n" + "="*120)

def main():
    """主函數"""
    print("💰 開始分析所有入金記錄...")
    
    try:
        analyzer = DepositAnalyzer()
        analysis_result = analyzer.analyze_all_deposits()
        
        if analysis_result:
            print_deposit_report(analysis_result)
            
            # 保存詳細分析到Excel
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"deposit_analysis_{timestamp}.xlsx"
            
            print(f"\n💾 正在保存入金分析到 {filename}...")
            
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 入金總結
                summary_data = []
                summary = analysis_result['all_deposits']
                for key, value in summary.items():
                    if key != 'coin_summary':
                        summary_data.append({'項目': key, '數值': value})
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='入金總結', index=False)
                
                # 幣種總結
                summary['coin_summary'].to_excel(writer, sheet_name='各幣種入金統計')
                
                # 操作類型統計
                analysis_result['by_operation'].to_excel(writer, sheet_name='按操作類型統計')
                
                # 幣種詳細統計
                analysis_result['by_coin'].to_excel(writer, sheet_name='按幣種詳細統計')
                
                # 年度統計
                analysis_result['by_time']['yearly'].to_excel(writer, sheet_name='按年度統計')
                
                # 月度統計
                analysis_result['by_time']['monthly'].to_excel(writer, sheet_name='按月度統計')
                
                # 帳戶類型統計
                analysis_result['by_account'].to_excel(writer, sheet_name='按帳戶類型統計')
                
                # 原始入金記錄
                analysis_result['raw_data'].to_excel(writer, sheet_name='完整入金記錄', index=False)
            
            print(f"✅ 入金分析已保存到: {filename}")
            
            # 額外保存純入金記錄CSV
            csv_filename = f"all_deposits_{timestamp}.csv"
            analysis_result['raw_data'].to_csv(csv_filename, index=False, encoding='utf-8')
            print(f"✅ 完整入金記錄CSV已保存到: {csv_filename}")
        
    except Exception as e:
        print(f"❌ 分析失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
